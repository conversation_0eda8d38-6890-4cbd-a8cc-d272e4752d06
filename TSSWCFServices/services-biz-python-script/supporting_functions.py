import datetime
from decimal import Decimal
from math import floor

import fn_CurveHelper as fn_curve
import numpy as np
import pandas as pd
from PythonFiles import MysqlAdapter2Java as MysqlAdapter
from usp_RevolvingInitialPoolDailyAggregation import (
    func as usp_RevolvingInitialPoolDailyAggregation,
)
from usp_SimulateCashflowByScenario_RevolvingRevolvingPoolDaily import (
    func as usp_SimulateCashflowByScenario_RevolvingRevolvingPoolDaily,
)

# 进行资产端现金流预测时，测算到从封包日起第[循环月数+36个月]月末
PREDICTION_MONTHS = 36

# 获取是否计算回购折价率
def get_is_calculate_repurchase_price(trust_id):
    sql = f"""
        select is_calculate_repurchase_rate
        from Analysis_BondRepurchaseSettingExt
        where TrustID = {trust_id};
    """
    res = MysqlAdapter.commonExecuteGetData(sql)

    return int(res[0][0]) if res and res[0][0] else 0

def get_repurchase_price_basis(trust_id):
    sql = f"""
        select CalculateBasis
        from Analysis_BondRepurchaseSettingExt
        where TrustID = {trust_id};
    """
    res = MysqlAdapter.commonExecuteGetData(sql)

    return res[0][0] if res and res[0][0] else None

def get_trust_period(trust_id):
    sql = f"""
        select StartDate, EndDate, ROW_NUMBER() over (order by EndDate asc) as Period
        from TrustManagement_TrustPeriod
        where TrustID = {trust_id} and TrustPeriodType = 'CollectionDate_NW'
    """
    df_trust_period = MysqlAdapter.exec_sql_to_df(sql)

    # StartDate and EndDate could be str, convert to datetime.date
    df_trust_period["StartDate"] = pd.to_datetime(df_trust_period["StartDate"]).dt.date
    df_trust_period["EndDate"] = pd.to_datetime(df_trust_period["EndDate"]).dt.date

    return df_trust_period

def get_solution_extension_params(trust_id):
    sql = f"""
        select ItemCode, ItemValue
        from Analysis_SolutionExtension
        where SolutionID = {trust_id};
    """
    res = MysqlAdapter.commonExecuteGetData(sql)

    # create a dictionary of ItemCode, ItemValue
    dict_params = {}
    for item in res:
        dict_params[item[0]] = item[1]

    return dict_params

def get_trust_extension_params(trust_id):
    sql = f"""
        select ItemCode, ItemValue
        from TrustManagement_TrustExtension
        where TrustID = {trust_id};
    """
    res = MysqlAdapter.commonExecuteGetData(sql)

    # create a dictionary of ItemCode, ItemValue
    dict_params = {}
    for item in res:
        dict_params[item[0]] = item[1]

    return dict_params

def get_revolving_date_range(trust_id):

    end_date = get_revolving_end_date(trust_id)

    sql = f"""
        select STR_TO_DATE(ItemValue, '%Y-%m-%d')
        from Analysis_SolutionExtension
        where SolutionID = {trust_id} and ItemCode = 'SimulationStartDate';
    """
    res = MysqlAdapter.commonExecuteGetData(sql)
    start_date = res[0][0] if res and res[0][0] else None

    if not start_date:
        sql = f"""
            select STR_TO_DATE(ItemValue, '%Y-%m-%d')
            from TrustManagement_TrustExtension
            where TrustID = {trust_id} and ItemCode = 'TrustStartDate';
        """
        res = MysqlAdapter.commonExecuteGetData(sql)
        start_date = res[0][0] if res and res[0][0] else None

    # convert str to datetime.date
    start_date = pd.to_datetime(start_date).date() if start_date else None

    return start_date, end_date

def get_revolving_end_date(trust_id):
    sql = f"""
        select max(start)
        from TrustManagement_tblCalendarDataSource
        where id = {trust_id} and Source = 'CollectionDate' and SourceType = 'R';
    """
    end_date = MysqlAdapter.commonExecuteGetData(sql)[0][0]

    # 美团：如果日期设置中有循环购买结束日，则使用该日期
    sql = f"""
        select cast(ItemValue as date) from TrustManagement_TrustExtension where TrustID = {trust_id} and ItemCode = 'RevolvingEndDate';
    """
    res = MysqlAdapter.commonExecuteGetData(sql)
    end_date = res[0][0] if res and res[0][0] else end_date

    # convert str to datetime.date
    end_date = pd.to_datetime(end_date).date() if end_date else None

    return end_date

def get_last_collection_date(trust_id):
    sql = f"""
        select max(EndDate)
        from TrustManagement_TrustPeriod
        where TrustID = {trust_id} and TrustPeriodType = 'CollectionDate_NW';
    """
    last_collection_date = MysqlAdapter.commonExecuteGetData(sql)[0][0]

    # convert str to datetime.date
    last_collection_date = pd.to_datetime(last_collection_date).date() if last_collection_date else None

    return last_collection_date

def get_simulation_start_date(trust_id):
    sql = f"""
        select ItemValue
        from Analysis_SolutionExtension
        where SolutionID = {trust_id} and ItemCode = 'SimulationStartDate';
    """
    res = MysqlAdapter.commonExecuteGetData(sql)
    simulation_start_date = res[0][0] if res and res[0][0] else None

    return simulation_start_date

def get_is_revolving(trust_id):
    sql = f"""
        select case ItemValue when 'true' then 1 else 0 end
        from TrustManagement_TrustExtension
        where TrustID = {trust_id} and ItemCode = 'IsTopUpAvailable';
    """
    res = MysqlAdapter.commonExecuteGetData(sql)
    is_product_revolving = res[0][0] if res and res[0][0] else 0

    return is_product_revolving

def get_trust_dates(trust_id):
    sql = f"""
        select ItemValue
        from Analysis_SolutionExtension
        where SolutionID = {trust_id} and ItemCode = 'SimulationStartDate';
    """
    res = MysqlAdapter.commonExecuteGetData(sql)
    simulation_start_date = res[0][0] if res and res[0][0] else None

    # convert to date type
    #simulation_start_date = datetime.datetime.strptime(simulation_start_date, '%Y-%m-%d').date() if simulation_start_date else None

    sql = f"""
        select ItemCode, ItemValue
        from TrustManagement_TrustExtension
        where TrustID = {trust_id} and ItemCode in ('TrustStartDate', 'PoolCloseDate');
    """
    res = MysqlAdapter.commonExecuteGetData(sql)
    trust_start_date = None
    pool_close_date = None
    for item in res:
        if item[0] == 'TrustStartDate':
            trust_start_date = item[1]
        elif item[0] == 'PoolCloseDate':
            pool_close_date = item[1]

    # trust_start_date = res[0][0] if res and res[0][0] else None

    # trust_start_date = datetime.datetime.strptime(trust_start_date, '%Y-%m-%d').date() if trust_start_date else None

    # sql = f"""
    #     select ItemValue
    #     from TrustManagement_TrustExtension
    #     where TrustID = {trust_id} and ItemCode = 'PoolCloseDate';
    # """
    # res = MysqlAdapter.commonExecuteGetData(sql)
    # pool_close_date = res[0][0] if res and res[0][0] else None

    # pool_close_date = datetime.datetime.strptime(pool_close_date, '%Y-%m-%d').date() if pool_close_date else None

    return simulation_start_date, trust_start_date, pool_close_date

def get_amortisation_curves(trust_id):
    amortisation_curves = []

    sql = f"""
        select AccountNo, SeqNo, EndDate, ScheduledPrincipal, ScheduledInterest, OpeningBalance
        , case when PrincipalPercent > 1 then 1 else PrincipalPercent end as PrincipalPercent
        , InterestRate
        from Analysis_GroupedPoolCashflowAmortisationPlan
        where TrustID = {trust_id}
        order by EndDate;
    """
    amortisation_curves = MysqlAdapter.commonExecuteGetData(sql)

    if len(amortisation_curves) == 0:
        print("[WARNING] 未找到存续池摊还计划, sql:", sql)

    return amortisation_curves

"""
    CREATE TABLE `daily_rev_plan_amortisation_plan` (
    `trust_id` int NOT NULL,
    `plan_id` int NOT NULL,
    `group_id` varchar(20) NOT NULL,
    `seq_no` int NOT NULL,
    `payment_date` date NOT NULL,
    `principal` decimal(19,2) DEFAULT NULL,
    `interest` decimal(19,2) DEFAULT NULL,
    `opening_balance` decimal(19,2) DEFAULT NULL,
    `principal_percent` decimal(19,4) DEFAULT NULL,
    `interest_rate` decimal(19,4) DEFAULT NULL,
    PRIMARY KEY (`trust_id`,`plan_id`,`group_id`,`seq_no`)
    ) ENGINE=InnoDB COMMENT='循环池资产摊还计划';
"""
def get_revolving_pool_amortisation_curves(trust_id, plan_id):
    amortisation_curves = []

    sql = f"""
        select group_id, seq_no, payment_date, principal, interest, opening_balance, principal_percent, interest_rate
        from daily_rev_plan_amortisation_plan
        where trust_id = {trust_id} and plan_id = {plan_id}
        order by payment_date;
    """
    print(sql)
    amortisation_curves = MysqlAdapter.commonExecuteGetData(sql)

    if len(amortisation_curves) == 0:
        print("[WARNING] 未找到循环池摊还计划, sql:", sql)

    return amortisation_curves

'''
example data:
dict_all_curves = {
    '1_1_1_3': [
        ('DefaultRate', 'Data1', '1', '1-30'),
        ('PrepaymentRate', 'Data2', '1', '1-30'),
        ('TransitionRate', 'Data5', '1', '1-30'),
        ('TransitionRate', 'Data6', '1', '31-60'),
        ('TransitionRate', 'Data7', '1', '61-90'),
        ('TransitionRate', 'Data8', '1', '91-120'),
        ('TransitionRate', 'Data9', '1', '121-150'),
        ('TransitionRate', 'Data10', '1', '151-180
    ],
    '1_2_1_3': [
        ('DefaultRate', 'Data3', '2', '1-30'),
        ('PrepaymentRate', 'Data4', '2', '1-30'),
        ('TransitionRate', 'Data11', '2', '1-30'),
        ('TransitionRate', 'Data12', '2', '31-60'),
        ('TransitionRate', 'Data13', '2', '61-90'),
        ('TransitionRate', 'Data14', '2', '91-120'),
        ('TransitionRate', 'Data15', '2', '121-150'),
        ('TransitionRate', 'Data16', '2', '151-180
    ]
}

'''
def get_curve_data_by_group(dict_all_curves, group_id_to_match):
    # curves_for_group always has 8 elements
    #print("group_id_to_match:", group_id_to_match)
    #print("keys of dict_all_curves:", dict_all_curves.keys())
    curves_for_group = dict_all_curves.get(group_id_to_match, [])

    #print("curves_for_group:", curves_for_group)

    default_curve_data = [curve[1] for curve in curves_for_group if curve[0] == 'DefaultRate']

    #print("default_curve_data:", default_curve_data)

    if default_curve_data:
        default_curve_data = default_curve_data[0]
    else:
        default_curve_data = '0'

    prepayment_curve_data = [curve[1] for curve in curves_for_group if curve[0] == 'PrepaymentRate']

    if prepayment_curve_data:
        prepayment_curve_data = prepayment_curve_data[0]
    else:
        prepayment_curve_data = '0'

    m1_curve_data = [curve[1] for curve in curves_for_group if curve[0] == 'TransitionRate' and curve[3] == '[ArrearsRate1_30]']

    if m1_curve_data:
        m1_curve_data = m1_curve_data[0]
    else:
        m1_curve_data = '1'

    m2_curve_data = [curve[1] for curve in curves_for_group if curve[0] == 'TransitionRate' and curve[3] == '[ArrearsRate31_60]']

    if m2_curve_data:
        m2_curve_data = m2_curve_data[0]
    else:
        m2_curve_data = '1'

    m3_curve_data = [curve[1] for curve in curves_for_group if curve[0] == 'TransitionRate' and curve[3] == '[ArrearsRate61_90]']

    if m3_curve_data:
        m3_curve_data = m3_curve_data[0]
    else:
        m3_curve_data = '1'

    m4_curve_data = [curve[1] for curve in curves_for_group if curve[0] == 'TransitionRate' and curve[3] == '[ArrearsRate91_120]']

    if m4_curve_data:
        m4_curve_data = m4_curve_data[0]
    else:
        m4_curve_data = '1'

    m5_curve_data = [curve[1] for curve in curves_for_group if curve[0] == 'TransitionRate' and curve[3] == '[ArrearsRate121_150]']

    if m5_curve_data:
        m5_curve_data = m5_curve_data[0]
    else:
        m5_curve_data = '1'

    m6_curve_data = [curve[1] for curve in curves_for_group if curve[0] == 'TransitionRate' and curve[3] == '[ArrearsRate151_180]']

    if m6_curve_data:
        m6_curve_data = m6_curve_data[0]
    else:
        m6_curve_data = '1'

    return default_curve_data, prepayment_curve_data, m1_curve_data, m2_curve_data, m3_curve_data, m4_curve_data, m5_curve_data, m6_curve_data

def get_asset_groups_from_asset_payment_status(trust_id):
    sql = f"""
        select s.AccountNo
        , s.LoanAmount as Amount
        , s.RemainingBalance as CurrentPrincipalBalance
        , s.RemainingBalance - ifnull(a.Arrears, 0) as PerformingLoanBalance
        , s.RemainingTerm as LoanTerm
        , s.RemainingTerm as RemainingTerm
        , ifnull(a.Arrears, 0) as CumulativeDefault
        , ifnull(s.Prepayment, 0.00) as CumulativePrepayment
        , ifnull(s.DaysInArrears, 0) as DaysInArrears
        , s.Seasoning
        , s.seasoning_at_purchase as SeasoningAtPurchase
        from Analysis_AssetPaymentStatus s
        left join (
            select AccountNo
            , ifnull(M1,0) + ifnull(M2,0) + ifnull(M3,0) + ifnull(M4,0) + ifnull(M5,0) + ifnull(M6,0) + ifnull(M6plus,0) as Arrears
            from Analysis_AssetArrearsDetails
            where TrustID = {trust_id}
        ) a on s.AccountNo = a.AccountNo
        where s.TrustID = {trust_id} and s.RemainingBalance > 0;
    """
    asset_groups = MysqlAdapter.commonExecuteGetData(sql)

    return asset_groups

def get_sensitivity_multipliers(cdr_values, cdr_step_id, cpr_values, cpr_step_id):
    cdr_multiply_factor = cdr_values.split(',')[cdr_step_id]
    cpr_multiply_factor = cpr_values.split(',')[cpr_step_id]

    return Decimal(cdr_multiply_factor), Decimal(cpr_multiply_factor)

def get_sensitivity_multiply_factor(trust_id, base_scenario_id, sensitivity_mode, cdr_step_id, cpr_step_id):
    sql = f'''
        select DefaultRatePointValues, PrepaymentRatePointValues
        from Analysis_SensitivityAnalysisMultipleSettings
        where TrustID = {trust_id} and ScenarioID = {base_scenario_id} and SensitivityMode = '{sensitivity_mode}' and Type = 1;
    '''
    res = MysqlAdapter.commonExecuteGetData(sql)
    if len(res) == 0:
        print(sql)
        raise ValueError("未找到敏感性分析设置")

    cdr_values = res[0][0]
    cpr_values = res[0][1]

    return get_sensitivity_multipliers(cdr_values, cdr_step_id, cpr_values, cpr_step_id)

def get_curves_from_curve_group(curve_group_id):
    sql = f"""
        select c.CurveTypeCode, c.CurveData, a.AttributeValue as group_id, a1.AttributeValue as Section
        from Analysis_CurveGroupCurves gc
        inner join dbo_Curves c on c.Id = gc.CurveId
        inner join dbo_CurveAttributes a on a.CurveId = c.Id and a.AttributeName = 'group_id'
        left join dbo_CurveAttributes a1 on a1.CurveId = c.Id and a1.AttributeName = 'Section'
        where GroupId = {curve_group_id}
        order by c.CurveTypeCode, a.AttributeValue;
    """
    all_curves = MysqlAdapter.commonExecuteGetData(sql)

    return all_curves

def make_curves_dict(all_curves):
    # make a dictionary of all_curves, with key as group_id
    # each group_id has a list of tuples, each tuple is a curve (CurveTypeCode, CurveData, group_id, Section)
    dict_all_curves = {}
    for curve in all_curves:
        group_id = curve[2]
        if group_id not in dict_all_curves:
            dict_all_curves[group_id] = []

        # for CurveTypeCode = 'TransitionRate', make any value greater than 1 as 1
        if curve[0] == 'TransitionRate':
            curve_data = ','.join([str(min(1, Decimal(value))) for value in curve[1].split(",")])
            curve = (curve[0], curve_data, curve[2], curve[3])

        dict_all_curves[group_id].append(curve)

    return dict_all_curves

def get_all_stress_curves(scenario_id):
    curve_group_id, multiplier, group_name = fn_curve.get_curve_group_id(scenario_id)
    print("curve_group_id=", curve_group_id, "multiplier=", multiplier)

    if curve_group_id == 0:
        print("[WARNING] 未找到曲线组!")

    all_curves = get_curves_from_curve_group(curve_group_id)

    # 曲线的group_id来源于静态池的group_id，例如：1_2_1_1
    # 1：一级分类
    # 2：二级分类
    # 3：风险分组
    # 4：合同期限
    # 注意没有账龄维度
    #print("all_curves:", all_curves)

    # make a dictionary of all_curves, with key as group_id
    # each group_id has a list of tuples, each tuple is a curve (CurveTypeCode, CurveData, group_id, Section)
    dict_all_curves = make_curves_dict(all_curves)

    return dict_all_curves, curve_group_id, group_name

def get_all_stress_curves_product_design(trust_id, base_scenario_id, sensitivity_mode, cdr_step_id, cpr_step_id):
    curve_group_id, multiplier, group_name = fn_curve.get_curve_group_id(base_scenario_id)
    print("curve_group_id=", curve_group_id, "multiplier=", multiplier)

    all_curves = get_curves_from_curve_group(curve_group_id)

    cdr_multiply_factor, cpr_multiply_factor = get_sensitivity_multiply_factor(trust_id, base_scenario_id, sensitivity_mode, cdr_step_id, cpr_step_id)

    print("违约率倍数:", cdr_multiply_factor, "早偿率倍数:", cpr_multiply_factor)

    new_all_curves = []
    for curve in all_curves:
        if curve[0] == "DefaultRate":
            multiplied_curve = (curve[0], ','.join([str(Decimal(value) * cdr_multiply_factor) for value in curve[1].split(",")]), curve[2], curve[3])
            new_all_curves.append(multiplied_curve)
        elif curve[0] == "PrepaymentRate":
            multiplied_curve = (curve[0], ','.join([str(Decimal(value) * cpr_multiply_factor) for value in curve[1].split(",")]), curve[2], curve[3])
            new_all_curves.append(multiplied_curve)

    # put the rest of the curves into new_all_curves
    new_all_curves.extend([curve for curve in all_curves if curve[0] not in ["DefaultRate", "PrepaymentRate"]])

    all_curves = new_all_curves

    # 曲线的group_id来源于静态池的group_id，例如：1_2_1_1
    # 1：一级分类
    # 2：二级分类
    # 3：风险分组
    # 4：合同期限
    # 注意没有账龄维度
    #print("all_curves:", all_curves)

    dict_all_curves = make_curves_dict(all_curves)

    return dict_all_curves, curve_group_id, group_name

def get_multiplied_stress_curves(curve_group_id, cdr_values, cdr_step_id, cpr_values, cpr_step_id):

    all_curves = get_curves_from_curve_group(curve_group_id)

    cdr_multiply_factor, cpr_multiply_factor = get_sensitivity_multipliers(cdr_values, cdr_step_id, cpr_values, cpr_step_id)

    print("违约率倍数:", cdr_multiply_factor, "早偿率倍数:", cpr_multiply_factor)

    new_all_curves = []
    for curve in all_curves:
        if curve[0] == "DefaultRate":
            multiplied_curve = (curve[0], ','.join([str(Decimal(value) * cdr_multiply_factor) for value in curve[1].split(",")]), curve[2], curve[3])
            new_all_curves.append(multiplied_curve)
            #print(multiplied_curve)
        elif curve[0] == "PrepaymentRate":
            multiplied_curve = (curve[0], ','.join([str(Decimal(value) * cpr_multiply_factor) for value in curve[1].split(",")]), curve[2], curve[3])
            new_all_curves.append(multiplied_curve)
    # put the rest of the curves into new_all_curves
    new_all_curves.extend([curve for curve in all_curves if curve[0] not in ["DefaultRate", "PrepaymentRate"]])

    all_curves = new_all_curves

    # 曲线的group_id来源于静态池的group_id，例如：1_2_1_1
    # 1：一级分类
    # 2：二级分类
    # 3：风险分组
    # 4：合同期限
    # 注意没有账龄维度
    #print("all_curves:", all_curves)

    dict_all_curves = make_curves_dict(all_curves)

    return dict_all_curves

def get_initial_principal_interest(trust_id):
    initial_principal = initial_interest = 0

    sql = f"""
        select ItemCode, cast(ItemValue as decimal(19,2))
        from Analysis_SolutionExtension
        where SolutionID = {trust_id} and ItemCode in ('InitialPrincipalCollection', 'InitialInterestCollection');
    """
    res = MysqlAdapter.commonExecuteGetData(sql)
    if len(res) == 2:
        initial_principal = [r[1] for r in res if r[0] == "InitialPrincipalCollection"][0]
        initial_interest = [r[1] for r in res if r[0] == "InitialInterestCollection"][0]

    return initial_principal, initial_interest

def calculate_initial_pool_stress_effect(trust_id, session_id, scenario_id, simulation_start_date):
    # 计算压力效应
    sql = f"""
        delete from Analysis_StressEffectOverview where SessionID = '{session_id}' and TrustID = {trust_id} and ScenarioID = {scenario_id} and RevolvingPurchaseID = 0;

        insert into Analysis_StressEffectOverview (TrustID, SessionID, ScenarioID, RevolvingPurchaseID, GroupID, ScheduledPrincipal, ScheduledInterest, StressedPrincipal, StressedInterest, StressedPrincipalRatio, StressedInterestRatio, TotalDefault, DefaultRatio, TotalRecovery, RecoveryRatio, seasoning, seasoning_at_purchase)
        select {trust_id}, '{session_id}', {scenario_id}, 0
        , a.AccountNo
        , a.P as ScheduledPrincipal
        , a.I as ScheduleInterest
        , b.P as StressedPrincipal
        , b.I as StressedInterest
        , case when a.P = 0 then 0 else b.P / a.P end as StressedPrincipalRatio
        , case when a.I = 0 then 0 else b.I / a.I end as StressedInterestRatio
        , b.D as TotalDefault
        , b.D / ps.LoanAmount as DefaultRatio
        , b.R as TotalRecovery
        , case when b.D + ps.AmountInArrears = 0 then 0 else b.R / (b.D + ps.AmountInArrears) end as RecoveryRatio
        , ps.Seasoning as seasoning
        , ps.seasoning_at_purchase
        from (
            select AccountNo, sum(ScheduledPrincipal) as P, sum(ScheduledInterest) as I
            from Analysis_GroupedPoolCashflowHistory
            where TrustID = {trust_id} and EndDate >= '{simulation_start_date}'
            group by AccountNo
        ) a
        inner join (
            select AccountNo, LoanAmount, AmountInArrears, Seasoning, seasoning_at_purchase
            from Analysis_AssetPaymentStatus
            where TrustID = {trust_id}
        ) ps on ps.AccountNo = a.AccountNo
        inner join (
            select GroupID, sum(Principal+Prepayment) as P
            , sum(Interest) as I
            , sum(DefaultPrincipal) as D
            , sum(RecoveryPrincipal) as R
            from Analysis_GroupedStressedCashflowDetails
            where TrustID = {trust_id} and SessionID = '{session_id}' and ScenarioID = {scenario_id} and RevolvingPurchaseID = 0
            group by GroupID
        ) b on b.GroupID = a.AccountNo
        order by a.P;
    """
    #print(sql)
    MysqlAdapter.commonRunsql(sql)

def calculate_revolving_pool_grouped_stress_effect(trust_id, session_id, scenario_id):
    # 计算压力效应
    sql = f"""
        delete from Analysis_StressEffectOverview where SessionID = '{session_id}' and TrustID = {trust_id} and ScenarioID = {scenario_id} and RevolvingPurchaseID = -2;

        insert into Analysis_StressEffectOverview (TrustID, SessionID, ScenarioID, RevolvingPurchaseID, GroupID, ScheduledPrincipal, ScheduledInterest, StressedPrincipal, StressedInterest, StressedPrincipalRatio, StressedInterestRatio, TotalDefault, DefaultRatio, TotalRecovery, RecoveryRatio, seasoning)
        select {trust_id}, '{session_id}', {scenario_id}, -2
        , a.group_id
        , a.P as ScheduledPrincipal
        , a.I as ScheduleInterest
        , b.P as StressedPrincipal
        , b.I as StressedInterest
        , case when a.P = 0 then NULL else b.P / a.P end as StressedPrincipalRatio
        , case when a.I = 0 then NULL else b.I / a.I end as StressedInterestRatio
        , b.D as TotalDefault
        , case when a.P = 0 then NULL else b.D / a.P end as DefaultRatio
        , b.R as TotalRecovery
        , case when b.D = 0 then NULL else b.R / b.D end as RecoveryRatio
        , g.seasoning
        from (
            select group_id, sum(principal) as P, sum(interest) as I
            from daily_rev_plan_cashflows_grouped
            where trust_id = {trust_id} and plan_id = 1
            group by group_id
        ) a
        inner join (
            select GroupID, sum(Principal+Prepayment) as P
            , sum(Interest) as I
            , sum(DefaultPrincipal) as D
            , sum(RecoveryPrincipal) as R
            from Analysis_GroupedStressedCashflowDetails
            where TrustID = {trust_id} and SessionID = '{session_id}' and ScenarioID = {scenario_id} and RevolvingPurchaseID = -2
            group by GroupID
        ) b on b.GroupID = a.group_id
        left join daily_rev_plan_groups g on g.trust_id = {trust_id} and g.plan_id = 1 and g.group_id = a.group_id
        order by a.P;
    """
    #print(sql)
    MysqlAdapter.commonRunsql(sql)

def calculate_average_revolving_pool_grouped_stress_effect(trust_id, session_id, scenario_id):

    sql = f"""
        delete from Analysis_StressEffectOverview where SessionID = '{session_id}' and TrustID = {trust_id} and ScenarioID = {scenario_id} and RevolvingPurchaseID = -3;

        insert into Analysis_StressEffectOverview (TrustID, SessionID, ScenarioID, RevolvingPurchaseID, GroupID, ScheduledPrincipal, ScheduledInterest, StressedPrincipal, StressedInterest, StressedPrincipalRatio, StressedInterestRatio, TotalDefault, DefaultRatio, TotalRecovery, RecoveryRatio, seasoning)
        select {trust_id}, '{session_id}', {scenario_id}, -3
        , a.group_id
        , a.P as ScheduledPrincipal
        , a.I as ScheduleInterest
        , b.P as StressedPrincipal
        , b.I as StressedInterest
        , case when a.P = 0 then NULL else b.P / a.P end as StressedPrincipalRatio
        , case when a.I = 0 then NULL else b.I / a.I end as StressedInterestRatio
        , b.D as TotalDefault
        , case when a.P = 0 then NULL else b.D / a.P end as DefaultRatio
        , b.R as TotalRecovery
        , case when b.D = 0 then NULL else b.R / b.D end as RecoveryRatio
        , g.seasoning
        from (
            select group_id, sum(principal) as P, sum(interest) as I
            from daily_rev_plan_cashflows_grouped
            where trust_id = {trust_id} and plan_id = -1
            group by group_id
        ) a
        inner join (
            select GroupID, sum(Principal+Prepayment) as P
            , sum(Interest) as I
            , sum(DefaultPrincipal) as D
            , sum(RecoveryPrincipal) as R
            from Analysis_GroupedStressedCashflowDetails
            where TrustID = {trust_id} and SessionID = '{session_id}' and ScenarioID = {scenario_id} and RevolvingPurchaseID = -3
            group by GroupID
        ) b on b.GroupID = a.group_id
        left join daily_rev_plan_groups g on g.trust_id = {trust_id} and g.plan_id = -1 and g.group_id = a.group_id
        order by a.P;
    """
    #print(sql)
    MysqlAdapter.commonRunsql(sql)

def get_pool_cashflow_history(trust_id):

    sql = f"""
        select EndDate as PayDate
        , PrincipalCollection as Principal
        , InterestCollection as Interest
        , CumulativeDefault
        , OpeningBalance
        , 0 as InterestPenalty
        , 0 as CumulativePrepayment
        , 0 as DefaultRateBase
        , 0 as PrepaymentRateBase
        , 0 as RecoveryRateBase
        , 0 as LoanAmount
        , 0 as DefaultPrincipal
        , 0 as Prepayment
        , 0 as RecoveryPrincipal
        , ROW_NUMBER() over (order by EndDate) as PeriodID
        , ifnull(RevolvingPurchase,0) as RevolvingPurchase
        from Analysis_PoolCashflowHistory
        where TrustID = {trust_id};
    """
    df_pool_cashflow_history = MysqlAdapter.exec_sql_to_df(sql)

    # fill NaN with 0
    df_pool_cashflow_history = df_pool_cashflow_history.fillna(0)

    # # convert all money columns from float to Decimal
    # df_pool_cashflow_history["Principal"] = df_pool_cashflow_history["Principal"].apply(lambda x: Decimal(x))
    # df_pool_cashflow_history["Interest"] = df_pool_cashflow_history["Interest"].apply(lambda x: Decimal(x))
    # df_pool_cashflow_history["DefaultPrincipal"] = df_pool_cashflow_history["DefaultPrincipal"].apply(lambda x: Decimal(x))
    # df_pool_cashflow_history["Prepayment"] = df_pool_cashflow_history["Prepayment"].apply(lambda x: Decimal(x))
    # df_pool_cashflow_history["RecoveryPrincipal"] = df_pool_cashflow_history["RecoveryPrincipal"].apply(lambda x: Decimal(x))
    # df_pool_cashflow_history["CumulativeDefault"] = df_pool_cashflow_history["CumulativeDefault"].apply(lambda x: Decimal(x))
    # df_pool_cashflow_history["OpeningBalance"] = df_pool_cashflow_history["OpeningBalance"].apply(lambda x: Decimal(x))
    # df_pool_cashflow_history["InterestPenalty"] = df_pool_cashflow_history["InterestPenalty"].apply(lambda x: Decimal(x))
    # df_pool_cashflow_history["CumulativePrepayment"] = df_pool_cashflow_history["CumulativePrepayment"].apply(lambda x: Decimal(x))
    # df_pool_cashflow_history["DefaultRateBase"] = df_pool_cashflow_history["DefaultRateBase"].apply(lambda x: Decimal(x))
    # df_pool_cashflow_history["PrepaymentRateBase"] = df_pool_cashflow_history["PrepaymentRateBase"].apply(lambda x: Decimal(x))
    # df_pool_cashflow_history["RecoveryRateBase"] = df_pool_cashflow_history["RecoveryRateBase"].apply(lambda x: Decimal(x))
    # df_pool_cashflow_history["LoanAmount"] = df_pool_cashflow_history["LoanAmount"].apply(lambda x: Decimal(x))

    if not df_pool_cashflow_history.empty:
        df_pool_cashflow_history["PayDate"] = pd.to_datetime(df_pool_cashflow_history["PayDate"]).dt.date
    else:
        # create an empty df with specified columns
        df_pool_cashflow_history = pd.DataFrame(columns=["PayDate", "Principal", "Interest", "CumulativeDefault", "OpeningBalance", "InterestPenalty", "CumulativePrepayment", "DefaultRateBase", "PrepaymentRateBase", "RecoveryRateBase", "LoanAmount", "DefaultPrincipal", "Prepayment", "RecoveryPrincipal", "PeriodID"])
        print("[WARNING] Analysis_PoolCashflowHistory表无数据")

    return df_pool_cashflow_history

def save_stressed_results(df_cashflow_details, df_mob_details):
    # round all numeric columns to 4 decimal places
    df_cashflow_details = df_cashflow_details.round(4)
    print("向Analysis_StressedCashflowDetails写入", df_cashflow_details.shape[0], "行数据")
    MysqlAdapter.dataframe_tosql(df_cashflow_details, "Analysis_StressedCashflowDetails")

    df_mob_details = df_mob_details.round(4)
    print("向Analysis_StressedCashflowArrearsDetails写入", df_mob_details.shape[0], "行数据")
    MysqlAdapter.dataframe_tosql(df_mob_details, "Analysis_StressedCashflowArrearsDetails")

    return ''

def save_cashflow_trust_period_aggregation(df_aggregation):
    # convert all numeric columns to 4 decimal places
    df_aggregation = df_aggregation.round(4)
    MysqlAdapter.dataframe_tosql(df_aggregation, "Analysis_StressedCashflowTrustPeriodAggregation")

def save_cashflow_arrears_aggregation(df_aggregation):
    MysqlAdapter.dataframe_tosql(df_aggregation, 'Analysis_StressedCashflowArrearsAggregation')

def calculate_revolving_pool_stress_effect(trust_id, session_id, scenario_id, dict_revolving_pool_casflow_details):
    if len(dict_revolving_pool_casflow_details) == 0:
        return

    # 先删除已存在的数据
    delete_sql = f"""
        delete from Analysis_RevolvingPoolStressOverview
        where TrustID = {trust_id} and SessionID = '{session_id}' and ScenarioID = {scenario_id}
    """
    MysqlAdapter.commonRunsql(delete_sql)

    # 对于dict_revolving_pool_casflow_details中的每个key(ProjectRemainingMonths)，计算sum(Principal), sum(Prepayment), sum(Interest), 保存到Analysis_RevolvingPoolStressOverview表
    insert_sql = """
        insert into Analysis_RevolvingPoolStressOverview (TrustID, SessionID, ScenarioID, ProjectRemainingMonths, TotalPrincipal, TotalPrepayment, TotalInterest, TotalDefault, TotalCollection) values
    """

    values = []

    for key, value in dict_revolving_pool_casflow_details.items():
        total_principal = value["Principal"].sum()
        total_prepayment = value["Prepayment"].sum()
        total_interest = value["Interest"].sum()
        total_default = value["DefaultPrincipal"].sum()
        total_collection = total_principal + total_prepayment + total_interest

        values.append(f"({trust_id}, '{session_id}', {scenario_id}, {key}, {total_principal}, {total_prepayment}, {total_interest}, {total_default}, {total_collection})")

    insert_sql += ', '.join(values)
    insert_sql += ";"
    #print(insert_sql)
    MysqlAdapter.commonRunsql(insert_sql)

def save_revolving_stressed_results(dict_revolving_pool_casflow_details, dict_revolving_pool_mob_details):
    # combine dataframes in dict_revolving_pool_casflow_details into one dataframe then write to db
    # 将key最大的数据写入向Analysis_StressedCashflowDetails
    max_key = max(dict_revolving_pool_casflow_details.keys())
    df_cashflow_details = dict_revolving_pool_casflow_details[max_key]
    df_mob_details = dict_revolving_pool_mob_details[max_key]
    MysqlAdapter.dataframe_tosql(df_cashflow_details, "Analysis_StressedCashflowDetails")
    MysqlAdapter.dataframe_tosql(df_mob_details, "Analysis_StressedCashflowArrearsDetails")

    # df_cashflow_details = None
    # df_mob_details = None

    # for key, value in dict_revolving_pool_casflow_details.items():
    #     if key == max_key:
    #         continue

    #     df = value.copy()
    #     df["ProjectRemainingMonths"] = key
    #     if df_cashflow_details is None:
    #         df_cashflow_details = df
    #     else:
    #         df_cashflow_details = df_cashflow_details.append(df, ignore_index=True)

    # # print("向Analysis_StressedCashflowDetails_Shortened写入", df_cashflow_details.shape[0], "行数据")
    # # MysqlAdapter.dataframe_tosql(df_cashflow_details, "Analysis_StressedCashflowDetails_Shortened")

    # for key, value in dict_revolving_pool_mob_details.items():
    #     if key == max_key:
    #         continue

    #     df = value.copy()
    #     df["ProjectRemainingMonths"] = key
    #     if df_mob_details is None:
    #         df_mob_details = df
    #     else:
    #         df_mob_details = df_mob_details.append(df, ignore_index=True)

    # # print("Analysis_StressedCashflowArrearsDetails_Shortened", df_mob_details.shape[0], "行数据")
    # # MysqlAdapter.dataframe_tosql(df_mob_details, "Analysis_StressedCashflowArrearsDetails_Shortened")

    return ''

def save_grouped_revolving_stressed_results(df_grouped_cashflow_details, dict_grouped_mob_details):
    print("向Analysis_GroupedStressedCashflowDetails写入", df_grouped_cashflow_details.shape[0], "行数据")
    MysqlAdapter.dataframe_tosql(df_grouped_cashflow_details, "Analysis_GroupedStressedCashflowDetails")

    max_key = max(dict_grouped_mob_details.keys())
    df_grouped_mob_details = dict_grouped_mob_details[max_key]

    # ignore the 'seasoning' column without affecting the orginal dataframe
    df_grouped_mob_details = df_grouped_mob_details.drop(columns=["seasoning"])

    print("向Analysis_GroupedArrearsDetails写入长度最长的循环池的MOB数据:", df_grouped_mob_details.shape[0], "行数据")
    MysqlAdapter.dataframe_tosql(df_grouped_mob_details, "Analysis_GroupedArrearsDetails")

def get_existing_arrears(trust_id):
    sql = f"""
        select AccountNo, M1,M2,M3,M4,M5,M6,M6plus
        from Analysis_AssetArrearsDetails where TrustID = {trust_id};
    """
    res = MysqlAdapter.commonExecuteGetData(sql)

    # create a dictionary, key = AccountNo, value is a tuple of (M1, M2, M3, M4, M5, M6, M6plus)
    dict_arrears = {}
    for item in res:
        dict_arrears[item[0]] = item[1:]

    return dict_arrears

def get_mob_price_discounts(trust_id):
    sql = f"""
        select M0, M1, M2, M3, M4, M5, M6, M6plus
        from Analysis_BondRepurchaseSettingExt
        where TrustID = {trust_id};
    """
    res = MysqlAdapter.commonExecuteGetData(sql)

    if len(res) == 0:
        return None

    # if none of the values is not 0, return None
    if all([r == 0 for r in res[0]]):
        return None

    # convert all values to float while keeping the tuple structure
    return tuple([float(r) for r in res[0]])

def calculate_call_price(df_mob_details, mob_price_discounts):
    m0_discount = mob_price_discounts[0] / 100.0
    m1_discount = mob_price_discounts[1] / 100.0
    m2_discount = mob_price_discounts[2] / 100.0
    m3_discount = mob_price_discounts[3] / 100.0
    m4_discount = mob_price_discounts[4] / 100.0
    m5_discount = mob_price_discounts[5] / 100.0
    m6_discount = mob_price_discounts[6] / 100.0
    m6plus_discount = mob_price_discounts[7] / 100.0

    # for each row in df_mob_details, RepurchaseValue = M0 * m0_discount + M1 * m1_discount + M2 * m2_discount + M3 * m3_discount + M4 * m4_discount + M5 * m5_discount + M6 * m6_discount + M6plus * m6plus_discount
    df_mob_details["RepurchaseValue"] = (
        df_mob_details["M0"] * m0_discount
        + df_mob_details["M1"] * m1_discount
        + df_mob_details["M2"] * m2_discount
        + df_mob_details["M3"] * m3_discount
        + df_mob_details["M4"] * m4_discount
        + df_mob_details["M5"] * m5_discount
        + df_mob_details["M6"] * m6_discount
        + df_mob_details["M6plus"] * m6plus_discount
    )

def add_existing_mob_details(df_mob_details, existing_mob, asset_groups, trust_id, simulation_start_date):
    existing_mob_date = simulation_start_date - datetime.timedelta(days=1)

    # M0 = sum(PerformingLoanBalance) from asset_groups
    m0 = sum([group[3] for group in asset_groups])

    # M1, ..., M6plus = sum(M1), ..., sum(M6plus) from existing_mob
    m1 = sum([mob[0] for mob in existing_mob.values()])
    m2 = sum([mob[1] for mob in existing_mob.values()])
    m3 = sum([mob[2] for mob in existing_mob.values()])
    m4 = sum([mob[3] for mob in existing_mob.values()])
    m5 = sum([mob[4] for mob in existing_mob.values()])
    m6 = sum([mob[5] for mob in existing_mob.values()])
    m6plus = sum([mob[6] for mob in existing_mob.values()])

    session_id = df_mob_details["SessionID"].iloc[0]
    trust_id = df_mob_details["TrustID"].iloc[0]
    scenario_id = df_mob_details["ScenarioID"].iloc[0]
    revolving_purchase_id = df_mob_details["RevolvingPurchaseID"].iloc[0]
    account_no = df_mob_details["AccountNo"].iloc[0]
    period_id = df_mob_details["PeriodID"].iloc[0] - 1

    # create a new row for df_mob_details at existing_mob_date
    # "SessionID", "TrustID", "ScenarioID", "RevolvingPurchaseID", "AccountNo", "PeriodID", "PayDate", "M0", "M1", "M2", "M3", "M4", "M5", "M6", "M6plus", "RepurchaseValue"
    new_row = {
        "SessionID": session_id,
        "TrustID": trust_id,
        "ScenarioID": scenario_id,
        "RevolvingPurchaseID": revolving_purchase_id,
        "AccountNo": account_no,
        "PeriodID": period_id,
        "PayDate": existing_mob_date,
        "M0": m0,
        "M1": m1,
        "M2": m2,
        "M3": m3,
        "M4": m4,
        "M5": m5,
        "M6": m6,
        "M6plus": m6plus,
        "RepurchaseValue": 0
    }

    df_mob_details = df_mob_details.append(new_row, ignore_index=True)

    #print("已存在的逾期分布:", new_row, "添加到df_mob_details")
    return df_mob_details

def get_default_asset_service_fee_rate(trust_id):
    sql = f"""
        select ItemValue
        from TrustManagement_TrustFee
        where TrustID = {trust_id}
        and (TrustFeeDisplayName like '%服务%报酬%' or TrustFeeDisplayName like '%服务%费')
        and TrustFeeDisplayName not like '%后端%'
        and TrustFeeDisplayName not like '%超额%'
        and ItemCode like '%_Ratio_%'
        limit 1;
    """
    res = MysqlAdapter.commonExecuteGetData(sql)

    if len(res) == 0:
        return float(0)

    return float(res[0][0])


def add_months(source_date, months):
    """计算从指定日期加上N个月后的月末日期"""
    # 计算目标年月
    year = source_date.year
    month = source_date.month + months + 1

    # 处理跨年
    while month > 12:
        month -= 12
        year += 1

    return datetime.date(year, month, 1) - datetime.timedelta(days=1)

def process_daily_revolving(params,
    actual_revolving_dates,
    additional_fund,
    df_current_pool_cashflow,
    df_current_pool_mob,
    df_current_pool_grouped_mob,
    dict_revolving_pool_casflow_details,
    dict_revolving_pool_mob_details,
    dict_grouped_revolving_pool_mob_details,
    dict_average_revolving_pool_casflow_details,
    dict_average_revolving_pool_mob_details,
    dict_average_grouped_revolving_pool_mob_details,
    revolving_plan_id,
    average_revolving_plan_id
):
    trust_id = params["TrustID"]

    if "RevolvingStartDate" not in params.keys() or "RevolvingEndDate" not in params.keys():
        start_date, end_date = get_revolving_date_range(trust_id)
    else:
        start_date = params["RevolvingStartDate"]
        end_date = params["RevolvingEndDate"]

    if start_date <= end_date:
        log_text = (
            "循环购买起始日:" + str(start_date) + ", 循环购买结束日:" + str(end_date)
        )
        print(log_text)

        params["AdditionalFund"] = additional_fund

        params["RevolvingPurchaseID"] = 1

        # 循环时用 list_daily_revolving 记录循环购买情况；完成后，用 list_daily_revolving 中记录的每日循环购买的现金流通过组装的insert语句写入 Analysis_DailyRevolving
        list_daily_revolving = []

        tuple_revolving = pd.date_range(start_date, end_date)

        # 无需限制 ReserveDate between '{start_date}' and '{end_date}'，因为当前日期start_date可能已经晚于最后一个预留费用日期
        sql = f"""
            select ReserveDate, ifnull(ReserveAmount, 0) as ReserveAmount
            from Analysis_RevolvingPurchaseReservePlan
            where TrustID = {trust_id}
            order by ReserveDate asc;
        """
        tuple_reserve = MysqlAdapter.commonExecuteGetData(sql)

        tuple_revolving_df = pd.DataFrame(tuple_revolving, columns=["Date"])
        tuple_reserve_df = pd.DataFrame(
            tuple_reserve, columns=["ReserveDate", "Reserve"]
        )
        tuple_reserve_df["ReserveDate"] = pd.to_datetime(
            tuple_reserve_df["ReserveDate"]
        )
        revolving_dates_df = pd.merge(
            tuple_revolving_df,
            tuple_reserve_df,
            how="left",
            left_on="Date",
            right_on="ReserveDate",
            validate="one_to_one"
        )
        revolving_dates_df = revolving_dates_df.drop(columns=["ReserveDate"])

        # 闲置资金作为负值计入储备缺口
        gap_reserve_amount = Decimal(0.00)

        project_final_collection_date = params["MaxCollectionDate"]

        remaining_amount = Decimal(0)

        time_revolving_start = datetime.datetime.now()

        total_days = revolving_dates_df.shape[0]
        skipped_days = 0

        # 当日模拟循环购买使用实时方案，后续使用平均方案
        use_average_revolving_pool = False

        # 性能改进
        grouped_mob_dfs = []  # 创建空列表存储所有DataFrame

        for index, row in revolving_dates_df.iterrows():
            revolving_date = row["Date"].date()
            reserve_amount = row["Reserve"]
            if pd.isna(reserve_amount):
                reserve_amount = Decimal("0")

            #print(f"Date: {revolving_date}, Reserve Amount: {reserve_amount}")

            skip_revolving_purchase = (
                False if revolving_date in actual_revolving_dates else True
            )

            if skip_revolving_purchase:
                skipped_days += 1

            # 例如如果循环购买日距离最后归集日还有192天，则月数为6，应选取剩余月数为6的循环池现金流
            project_remaining_days = (project_final_collection_date - revolving_date).days
            project_remaining_months = floor(project_remaining_days / 30)
            #print("产品剩余", project_remaining_days, "天, 选取剩余月数为", project_remaining_months, "的循环池现金流")

            if (
                use_average_revolving_pool
                and dict_average_revolving_pool_casflow_details
                and dict_average_revolving_pool_mob_details
                and dict_average_grouped_revolving_pool_mob_details
            ):
                plan_id = average_revolving_plan_id
                if project_remaining_months not in dict_average_revolving_pool_casflow_details:
                    raise Exception(f"未找到剩余月数为{project_remaining_months}的循环池现金流")
                if project_remaining_months not in dict_average_revolving_pool_mob_details:
                    raise Exception(f"未找到剩余月数为{project_remaining_months}的循环池MOB")

                df_revolving_pool_cashflow = dict_average_revolving_pool_casflow_details[project_remaining_months]
                df_revolving_pool_mob = dict_average_revolving_pool_mob_details[project_remaining_months]
                df_grouped_revolving_pool_mob = dict_average_grouped_revolving_pool_mob_details[project_remaining_months]

            else:
                plan_id = revolving_plan_id
                if project_remaining_months not in dict_revolving_pool_casflow_details:
                    raise Exception(f"未找到剩余月数为{project_remaining_months}的循环池现金流")
                if project_remaining_months not in dict_revolving_pool_mob_details:
                    raise Exception(f"未找到剩余月数为{project_remaining_months}的循环池MOB")

                df_revolving_pool_cashflow = dict_revolving_pool_casflow_details[project_remaining_months]
                df_revolving_pool_mob = dict_revolving_pool_mob_details[project_remaining_months]
                df_grouped_revolving_pool_mob = dict_grouped_revolving_pool_mob_details[project_remaining_months]

            #print("本次循环购买使用plan_id", plan_id)
            # 对循环池现金流进行日期偏移
            revolving_pool_first_pay_date = df_revolving_pool_cashflow["PayDate"].min().date()
            days_shift = (revolving_date - revolving_pool_first_pay_date).days + 1

            #print("叠加后现金流为:", df_current_pool_cashflow[["PayDate", "Principal"]])
            (
                gap_reserve_amount,
                remaining_amount,
                df_current_pool_cashflow,
                df_current_pool_mob,
                df_adjusted_grouped_mob
            ) = usp_SimulateCashflowByScenario_RevolvingRevolvingPoolDaily(
                params={
                    "SessionID": params["SessionID"],
                    "TrustID": params["TrustID"],
                    "ScenarioID": params["ScenarioID"],
                    "RevolvingPurchaseID": params["RevolvingPurchaseID"],
                    "RevolvingPurchaseDate": revolving_date,
                    "AssetPrincipal": params["InitialAmount"],
                    "AssetValue": params["AssetValue"],
                    "ReserveAmount": reserve_amount,
                    "GapReserveAmount": gap_reserve_amount,
                    "AdditionalFund": params["AdditionalFund"],
                    "DaysShift": days_shift,
                    "DiscountRate": params["DiscountRate"],
                    "RemainingAmount": remaining_amount,
                    "SkipPurchase": skip_revolving_purchase,
                    "StressedCashflowDetails": df_current_pool_cashflow,
                    "RevolvingPoolCashflowDetails": df_revolving_pool_cashflow,
                    "PoolMOB": df_current_pool_mob,
                    "RevolvingPoolMOB": df_revolving_pool_mob,
                    "GroupedRevolvingPoolMOB": df_grouped_revolving_pool_mob,
                    "DailyRevolving": list_daily_revolving,
                    "PlanID": plan_id
                }
            )

            if df_adjusted_grouped_mob is not None:
                grouped_mob_dfs.append(df_adjusted_grouped_mob)

            # 从第二次循环购买开始，使用平均循环池现金流和MOB
            if not skip_revolving_purchase:
                use_average_revolving_pool = True

            params["AdditionalFund"] = 0
            additional_fund = params["AdditionalFund"]
            params["RevolvingPurchaseID"] += 1

        print(f"现在一次性连接{len(grouped_mob_dfs)}个MOB dataframe")
        df_current_pool_grouped_mob = pd.concat([df_current_pool_grouped_mob] + grouped_mob_dfs)

        time_revolving_end = datetime.datetime.now()
        print("模拟日循环过程耗时：", (time_revolving_end - time_revolving_start).total_seconds(), "秒")
        print("共模拟", total_days, "天，跳过", skipped_days, "天")

        # 完成循环期后，将 list_daily_revolving 中记录的每日循环购买的现金流通过组装的insert语句写入 Analysis_DailyRevolving
        save_daily_revolving(params, trust_id, list_daily_revolving)

        print("开始对每日循环池现金流和MOB进行堆叠")
        time_aggregation_start = datetime.datetime.now()

        df_current_pool_cashflow, df_current_pool_mob = usp_RevolvingInitialPoolDailyAggregation(
            params={
                "SessionID": params["SessionID"],
                "TrustID": params["TrustID"],
                "ScenarioID": params["ScenarioID"],
                "StressedCashflowDetails": df_current_pool_cashflow,
                "StressedArrearsDetails": df_current_pool_mob,
                "DailyRevolving": list_daily_revolving,
                "MaxCollectionDate": params["MaxCollectionDate"]
            }
        )
        save_stressed_results(df_current_pool_cashflow, df_current_pool_mob)

        time_aggregation_end = datetime.datetime.now()
        print("循环池现金流和MOB堆叠耗时：", (time_aggregation_end - time_aggregation_start).total_seconds(), "秒")
    else:
        print("循环期已结束")

    return additional_fund, df_current_pool_cashflow, df_current_pool_mob, df_current_pool_grouped_mob

def save_daily_revolving(params, trust_id, list_daily_revolving):
    df_daily_revolving = pd.DataFrame(list_daily_revolving, columns=["RevolvingDate", "RevolvingAmount", "ReserveAmountGap", "RevolvingCPB", "ReservedAmount", "TotalCollection", "InterestCollection", "ReturnRate", "InterestReturnRate", "Profit", "RevolvingCashflowLength", "PlanID"])
    df_daily_revolving["TrustID"] = trust_id
    df_daily_revolving["SessionID"] = params["SessionID"]
    df_daily_revolving["ScenarioID"] = params["ScenarioID"]

    MysqlAdapter.dataframe_tosql(df_daily_revolving, "Analysis_DailyRevolving")

# 定义加权平均函数
def weighted_average(values, weights):
    if weights.sum() == 0:
        return 0
    return (values.sum() / weights.sum())

def save_grouped_mob_prediction(params, df_current_pool_grouped_mob, curve_group_id, df_trust_period):
    print("=== 保存分组MOB到Analysis_GroupedMobPrediction ===")

    # 将df_current_pool_grouped_mob的M0...M6plus中的所有负值置为0
    mob_columns = ['M0', 'M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M6plus']
    for col in mob_columns:
        df_current_pool_grouped_mob[col] = df_current_pool_grouped_mob[col].clip(lower=0)

    # 取必要字段
    #print("df_current_pool_grouped_mob 共有", df_current_pool_grouped_mob.shape[0], "行数据")
    valid_dates = df_trust_period['EndDate'].unique()

    df_original = df_current_pool_grouped_mob.loc[
        :, ["PayDate", "GroupID", "M0", "M1", "M2", "M3", "M4", "M5", "M6", "M6plus", "seasoning"]
    ].copy()
    df_original['PayDate'] = pd.to_datetime(df_original['PayDate']).dt.date
    df_original = df_original[df_original['PayDate'].isin(valid_dates)]
    # 2025-05-26 需要区分账龄，所以保留完整的GroupID
    # group by GroupID and PayDate, and sum M0 to M6plus
    df_grouped = df_original.groupby(['GroupID', 'PayDate', 'seasoning']).sum().reset_index()

    # 添加SessionID, TrustID, ScenarioID
    df_grouped['SessionID'] = params['SessionID']
    df_grouped['TrustID'] = params['TrustID']
    df_grouped['ScenarioID'] = params['ScenarioID']

    '''
        CREATE TABLE `Analysis_GroupedMobPrediction` (
        `Id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增id',
        `SessionID` varchar(50) NOT NULL COMMENT '会话ID',
        `TrustID` int NOT NULL COMMENT '产品ID',
        `ScenarioID` bigint NOT NULL COMMENT '情景ID',
        `PayDate` date NOT NULL COMMENT '日期',
        `GroupID` varchar(50) NOT NULL COMMENT '资产分组编号',
        `M0` decimal(24,6) NOT NULL COMMENT 'M0',
        `M1` decimal(24,6) NOT NULL COMMENT 'M1',
        `M2` decimal(24,6) NOT NULL COMMENT 'M2',
        `M3` decimal(24,6) NOT NULL COMMENT 'M3',
        `M4` decimal(24,6) NOT NULL COMMENT 'M4',
        `M5` decimal(24,6) NOT NULL COMMENT 'M5',
        `M6` decimal(24,6) NOT NULL COMMENT 'M6',
        `M6plus` decimal(24,6) NOT NULL COMMENT 'M6plus'
        `seasoning` int NOT NULL COMMENT '账龄(天)',
        PRIMARY KEY (`Id`),
        KEY `idx_trust_scenario` (`TrustID`,`ScenarioID`,`PayDate`,`GroupID`),
        KEY `idx_trust_session` (`TrustID`,`SessionID`)
        )
    '''
    # time_1 = datetime.datetime.now()
    # sql = """
    #     delete from Analysis_GroupedMobPrediction
    #     where TrustID = @TrustID and SessionID = '@SessionID' and ScenarioID = @ScenarioID;
    # """
    # sql = MysqlAdapter.prepareSql(sql, params)
    # MysqlAdapter.commonRunsql(sql)
    # time_2 = datetime.datetime.now()
    # print("删除Analysis_GroupedMobPrediction总耗时:", (time_2 - time_1).total_seconds(), "秒")

    # time_3 = datetime.datetime.now()
    # # print("df_grouped:", df_grouped.shape[0], "行数据, 开始写入")
    # MysqlAdapter.dataframe_tosql(df_grouped, "Analysis_GroupedMobPrediction")
    # time_4 = datetime.datetime.now()
    # print("写入Analysis_GroupedMobPrediction总耗时:", (time_4 - time_3).total_seconds(), "秒")

    '''
        CREATE TABLE `Analysis_GroupedMobPrice` (
        `Id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增id',
        `TrustID` int NOT NULL COMMENT '产品ID',
        `SessionID` varchar(50) NOT NULL COMMENT '会话ID',
        `ScenarioID` bigint NOT NULL COMMENT '情景ID',
        `PayDate` date NOT NULL COMMENT '日期',
        `M0` decimal(15,6) NOT NULL COMMENT 'M0',
        `M1` decimal(15,6) NOT NULL COMMENT 'M1',
        `M2` decimal(15,6) NOT NULL COMMENT 'M2',
        `M3` decimal(15,6) NOT NULL COMMENT 'M3',
        `M4` decimal(15,6) NOT NULL COMMENT 'M4',
        `M5` decimal(15,6) NOT NULL COMMENT 'M5',
        `M6` decimal(15,6) NOT NULL COMMENT 'M6',
        `M6plus` decimal(15,6) NOT NULL COMMENT 'M6plus'
        PRIMARY KEY (`Id`),
        KEY `idx_trust_scenario` (`TrustID`,`ScenarioID`,`PayDate`),
        KEY `idx_trust_session` (`TrustID`,`SessionID`)
        )
    '''
    sql = """
        delete from Analysis_GroupedMobPrice
        where TrustID = @TrustID and SessionID = '@SessionID' and ScenarioID = @ScenarioID;

        delete from analysis_grouped_mob_price_expanded
        where session_id = '@SessionID' and trust_id = @TrustID and scenario_id = @ScenarioID;
    """
    sql = MysqlAdapter.prepareSql(sql, params)
    MysqlAdapter.commonRunsql(sql)

    time_5 = datetime.datetime.now()
    # 每个MOB区间的加权平均回收率只考虑实际有贷款金额（权重大于0）的数据行

    sql = f"""
        insert into Analysis_GroupedMobPrice (TrustID, SessionID, ScenarioID, PayDate, M0, M1, M2, M3, M4, M5, M6, M6plus)
        select p.TrustID, p.SessionID, p.ScenarioID, p.PayDate
        , case when sum(case when ifnull(p.M0, 0) > 0 then ifnull(p.M0, 0) else 0 end) = 0 then 0
               else sum(case when ifnull(p.M0, 0) > 0 then p.M0 * ifnull(p1.m0, 0) else 0 end) /
                    sum(case when ifnull(p.M0, 0) > 0 then ifnull(p.M0, 0) else 0 end) end as M0
        , case when sum(case when ifnull(p.M1, 0) > 0 then ifnull(p.M1, 0) else 0 end) = 0 then 0
               else sum(case when ifnull(p.M1, 0) > 0 then p.M1 * ifnull(p1.m1, 0) else 0 end) /
                    sum(case when ifnull(p.M1, 0) > 0 then ifnull(p.M1, 0) else 0 end) end as M1
        , case when sum(case when ifnull(p.M2, 0) > 0 then ifnull(p.M2, 0) else 0 end) = 0 then 0
               else sum(case when ifnull(p.M2, 0) > 0 then p.M2 * ifnull(p1.m2, 0) else 0 end) /
                    sum(case when ifnull(p.M2, 0) > 0 then ifnull(p.M2, 0) else 0 end) end as M2
        , case when sum(case when ifnull(p.M3, 0) > 0 then ifnull(p.M3, 0) else 0 end) = 0 then 0
               else sum(case when ifnull(p.M3, 0) > 0 then p.M3 * ifnull(p1.m3, 0) else 0 end) /
                    sum(case when ifnull(p.M3, 0) > 0 then ifnull(p.M3, 0) else 0 end) end as M3
        , case when sum(case when ifnull(p.M4, 0) > 0 then ifnull(p.M4, 0) else 0 end) = 0 then 0
               else sum(case when ifnull(p.M4, 0) > 0 then p.M4 * ifnull(p1.m4, 0) else 0 end) /
                    sum(case when ifnull(p.M4, 0) > 0 then ifnull(p.M4, 0) else 0 end) end as M4
        , case when sum(case when ifnull(p.M5, 0) > 0 then ifnull(p.M5, 0) else 0 end) = 0 then 0
               else sum(case when ifnull(p.M5, 0) > 0 then p.M5 * ifnull(p1.m5, 0) else 0 end) /
                    sum(case when ifnull(p.M5, 0) > 0 then ifnull(p.M5, 0) else 0 end) end as M5
        , case when sum(case when ifnull(p.M6, 0) > 0 then ifnull(p.M6, 0) else 0 end) = 0 then 0
               else sum(case when ifnull(p.M6, 0) > 0 then p.M6 * ifnull(p1.m6, 0) else 0 end) /
                    sum(case when ifnull(p.M6, 0) > 0 then ifnull(p.M6, 0) else 0 end) end as M6
        , case when sum(case when ifnull(p.M6plus, 0) > 0 then ifnull(p.M6plus, 0) else 0 end) = 0 then 0
               else sum(case when ifnull(p.M6plus, 0) > 0 then p.M6plus * ifnull(p1.m6, 0) * 0.5 else 0 end) /
                    sum(case when ifnull(p.M6plus, 0) > 0 then ifnull(p.M6plus, 0) else 0 end) end as M6plus
        from Analysis_GroupedMobPrediction p
        inner join (select EndDate from TrustManagement_TrustPeriod where TrustID = @TrustID and TrustPeriodType = 'CollectionDate_NW') tp on p.PayDate = tp.EndDate
        left join Analysis_AssetPaymentStatus ps on ps.TrustID = p.TrustID and ps.AccountNo = p.GroupID
        left join (
            select group_id, m0, m1, m2, m3, m4, m5, m6, ifnull(seasoning_month, 1) as seasoning_month
            from repurchase_pricing
            where super_group_id = {curve_group_id}
        ) p1 on locate(concat(p1.group_id, '_'), p.GroupID) > 0
        and p1.seasoning_month = floor((datediff(p.PayDate, '@SimulationStartDate') + ps.Seasoning) / 30) + 1
        where p.TrustID = @TrustID and p.SessionID = '@SessionID' and p.ScenarioID = @ScenarioID
        group by p.TrustID, p.SessionID, p.ScenarioID, p.PayDate;
    """
    sql = MysqlAdapter.prepareSql(sql, params)
    #print(sql)
    #print("此为原始sql，暂不执行，改为使用dataframe操作")
    #MysqlAdapter.commonRunsql(sql)
    #time_6 = datetime.datetime.now()
    #print("每日MOB价格已成功写入 Analysis_GroupedMobPrice")
    #print("写入Analysis_GroupedMobPrice总耗时:", (time_6 - time_5).total_seconds(), "秒")

    # 将价格匹配明细写入 analysis_grouped_mob_price_expanded
    sql = f"""
        delete from analysis_grouped_mob_price_expanded where session_id = '@SessionID' and trust_id = @TrustID and scenario_id = @ScenarioID;

        insert into analysis_grouped_mob_price_expanded (trust_id, session_id, scenario_id, pay_date, group_id
        , m0, m1, m2, m3, m4, m5, m6, m6plus
        , pricing_m0, pricing_m1, pricing_m2, pricing_m3, pricing_m4, pricing_m5, pricing_m6, pricing_m6plus
        , price_m0, price_m1, price_m2, price_m3, price_m4, price_m5, price_m6, price_m6plus)

        select p.TrustID, p.SessionID, p.ScenarioID, p.PayDate, p.GroupID
        , p.M0, p.M1, p.M2, p.M3, p.M4, p.M5, p.M6, p.M6plus
        , p1.m0, p1.m1, p1.m2, p1.m3, p1.m4, p1.m5, p1.m6, p1.m6
        , p.M0 * p1.m0
        , p.M1 * p1.m1
        , p.M2 * p1.m2
        , p.M3 * p1.m3
        , p.M4 * p1.m4
        , p.M5 * p1.m5
        , p.M6 * p1.m6
        , p.M6plus * p1.m6 * 0.5
        from Analysis_GroupedMobPrediction p
        inner join (select EndDate from TrustManagement_TrustPeriod where TrustID = @TrustID and TrustPeriodType = 'CollectionDate_NW') tp on p.PayDate = tp.EndDate
        left join Analysis_AssetPaymentStatus ps on ps.TrustID = p.TrustID and ps.AccountNo = p.GroupID
        left join (
            select group_id, m0, m1, m2, m3, m4, m5, m6, ifnull(seasoning_month, 1) as seasoning_month
            from repurchase_pricing
            where super_group_id = {curve_group_id}
        ) p1 on locate(concat(p1.group_id, '_'), p.GroupID) > 0
        and p1.seasoning_month = floor((datediff(p.PayDate, '@SimulationStartDate') + ps.Seasoning) / 30) + 1
        where p.TrustID = @TrustID and p.SessionID = '@SessionID' and p.ScenarioID = @ScenarioID;
    """
    sql = MysqlAdapter.prepareSql(sql, params)
    #print(sql)
    #print("此为原始sql，暂不执行，改为使用dataframe操作")
    #MysqlAdapter.commonRunsql(sql)
    #time_7 = datetime.datetime.now()
    #print("MOB价格计算展开已写入 analysis_grouped_mob_price_expanded")
    #print("写入analysis_grouped_mob_price_expanded总耗时:", (time_7 - time_6).total_seconds(), "秒")

    # 新实现：使用dataframe操作
    try:
        # 计算seasoning_month
        df_original['seasoning_month'] = np.floor(df_original['seasoning'] / 30).astype(int) + 1
        # drop seasoning column then rename seasoning_month to seasoning
        df_original = df_original.drop(columns=['seasoning']).rename(columns={'seasoning_month': 'seasoning'})

        # 2025-05-26 需要区分账龄，所以保留完整的GroupID
        # group by GroupID and PayDate and seasoning, and sum M0 to M6plus
        df_grouped = df_original.groupby(['GroupID', 'PayDate', 'seasoning']).sum().reset_index()

        # 添加SessionID, TrustID, ScenarioID
        df_grouped['SessionID'] = params['SessionID']
        df_grouped['TrustID'] = params['TrustID']
        df_grouped['ScenarioID'] = params['ScenarioID']
        df_grouped['PayDate'] = pd.to_datetime(df_grouped['PayDate'])

        # 再将GroupID最后一个下划线后的数字去掉，作为后面join的key
        df_grouped['group_id'] = df_grouped['GroupID'].str.split('_').str[:-1].str.join('_')

        # 将repurchase_pricing读入 df_repurchase_pricing
        sql = f"""
            select group_id, m0 as pricing_m0, m1 as pricing_m1, m2 as pricing_m2, m3 as pricing_m3, m4 as pricing_m4, m5 as pricing_m5, m6 as pricing_m6, m6 * 0.5 as pricing_m6plus, ifnull(seasoning_month, 1) as seasoning_month
            from repurchase_pricing
            where super_group_id = {curve_group_id}
        """
        df_repurchase_pricing = MysqlAdapter.exec_sql_to_df(sql)

        if df_repurchase_pricing.empty:
            print("repurchase_pricing中未找到super_group_id =", curve_group_id, "的数据")
            # 设置dataframe的列
            df_repurchase_pricing = pd.DataFrame(columns=['group_id', 'pricing_m0', 'pricing_m1', 'pricing_m2', 'pricing_m3', 'pricing_m4', 'pricing_m5', 'pricing_m6', 'pricing_m6plus', 'seasoning_month'])
            df_pricing_max_seasoning_month = pd.DataFrame(columns=['group_id', 'pricing_m0', 'pricing_m1', 'pricing_m2', 'pricing_m3', 'pricing_m4', 'pricing_m5', 'pricing_m6', 'pricing_m6plus'])
        else:
            # 对df_repurchase_pricing中每个group_id，获取其最大的seasoning_month对应的pricing_m0到pricing_m6plus
            df_pricing_max_seasoning_month = df_repurchase_pricing.groupby('group_id').apply(lambda x: x.loc[x['seasoning_month'].idxmax()]).reset_index(drop=True)
            df_pricing_max_seasoning_month = df_pricing_max_seasoning_month[['group_id', 'pricing_m0', 'pricing_m1', 'pricing_m2', 'pricing_m3', 'pricing_m4', 'pricing_m5', 'pricing_m6', 'pricing_m6plus']]

        # 通过df_grouped和df_repurchase_pricing进行join操作得到 df_grouped_mob_price，再写入Analysis_GroupedMobPrice
        df_merged = df_grouped.merge(df_repurchase_pricing, left_on=['group_id', 'seasoning'], right_on=['group_id', 'seasoning_month'], how='left')
        df_merged = df_merged[['TrustID', 'SessionID', 'ScenarioID', 'PayDate', 'group_id', 'seasoning', 'M0', 'M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M6plus', 'pricing_m0', 'pricing_m1', 'pricing_m2', 'pricing_m3', 'pricing_m4', 'pricing_m5', 'pricing_m6', 'pricing_m6plus']]

        df_merged = df_merged.merge(df_pricing_max_seasoning_month,
                            on='group_id',
                            how='left',
                            suffixes=('', '_max'))

        price_cols = ['pricing_m0', 'pricing_m1', 'pricing_m2', 'pricing_m3',
                    'pricing_m4', 'pricing_m5', 'pricing_m6', 'pricing_m6plus']
        for col in price_cols:
            df_merged[col] = df_merged[col].fillna(df_merged[f'{col}_max'])

        df_merged = df_merged.drop([f'{col}_max' for col in price_cols], axis=1)

        # 参考sql中的case when和sum逻辑，使用pandas的向量化操作
        # 为所有M列创建辅助列，用于计算
        cols_to_process = ['M0', 'M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M6plus']

        # 为每个列创建weight和weighted_value列
        for i, (m_col, m_price_col) in enumerate(zip(cols_to_process, price_cols)):
            # 计算权重 (M值，如果>0)
            df_merged[f'weight_{m_col}'] = df_merged[m_col].where(df_merged[m_col] > 0, 0)

            # 计算加权值 (M * m)
            df_merged[f'weighted_{m_col}'] = df_merged[f'weight_{m_col}'] * df_merged[m_price_col].fillna(0)

        # 使用groupby和agg一次性计算所有加权平均
        aggregations = {}
        for m_col in cols_to_process:
            aggregations[m_col] = lambda x, col=m_col: weighted_average(
                x[f'weighted_{col}'],
                x[f'weight_{col}']
            )

        # 一次性应用聚合函数
        df_grouped_mob_price = df_merged.groupby(
            ['TrustID', 'SessionID', 'ScenarioID', 'PayDate']
        ).apply(
            lambda group: pd.Series({
                col: weighted_average(
                    group[f'weighted_{col}'],
                    group[f'weight_{col}']
                ) for col in cols_to_process
            })
        ).reset_index()

        # 写入数据库
        MysqlAdapter.dataframe_tosql(df_grouped_mob_price, 'Analysis_GroupedMobPrice')

        time_6 = datetime.datetime.now()
        print("使用DataFrame写入Analysis_GroupedMobPrice成功")
        print("DataFrame处理耗时:", (time_6 - time_5).total_seconds(), "秒")

        # 利用df_merged写入analysis_grouped_mob_price_expanded
        '''
            insert into analysis_grouped_mob_price_expanded (trust_id, session_id, scenario_id, pay_date, group_id, seasoning
            , m0, m1, m2, m3, m4, m5, m6, m6plus
            , pricing_m0, pricing_m1, pricing_m2, pricing_m3, pricing_m4, pricing_m5, pricing_m6, pricing_m6plus
            , price_m0, price_m1, price_m2, price_m3, price_m4, price_m5, price_m6, price_m6plus)
        '''

        for i, (m_col, pricing_col) in enumerate(zip(cols_to_process, price_cols)):
            df_merged[f'price_{m_col.lower()}'] = df_merged[m_col] * df_merged[pricing_col]

        df_merged = df_merged.rename(columns={
            'TrustID': 'trust_id',
            'SessionID': 'session_id',
            'ScenarioID': 'scenario_id',
            'PayDate': 'pay_date',
            'M0': 'm0',
            'M1': 'm1',
            'M2': 'm2',
            'M3': 'm3',
            'M4': 'm4',
            'M5': 'm5',
            'M6': 'm6',
            'M6plus': 'm6plus'
        })

        df_merged = df_merged[['trust_id', 'session_id', 'scenario_id', 'pay_date', 'group_id', 'seasoning', 'm0', 'm1', 'm2', 'm3', 'm4', 'm5', 'm6', 'm6plus', 'pricing_m0', 'pricing_m1', 'pricing_m2', 'pricing_m3', 'pricing_m4', 'pricing_m5', 'pricing_m6', 'pricing_m6plus', 'price_m0', 'price_m1', 'price_m2', 'price_m3', 'price_m4', 'price_m5', 'price_m6', 'price_m6plus']]

        sql = """
            delete from analysis_grouped_mob_price_expanded
            where session_id = '@SessionID' and trust_id = @TrustID and scenario_id = @ScenarioID;
        """
        sql = MysqlAdapter.prepareSql(sql, params)
        MysqlAdapter.commonRunsql(sql)

        # 写入analysis_grouped_mob_price_expanded
        MysqlAdapter.dataframe_tosql(df_merged, 'analysis_grouped_mob_price_expanded')

        # 保存情景id以及对应的情景描述
        scenario_desc = get_scenario_desc_by_id(params)

        # 先清理后写入analysis_grouped_mob_scenario_desc
        sql = """
            delete from analysis_grouped_mob_scenario_desc where session_id = '@SessionID' and trust_id = @TrustID and scenario_id = @ScenarioID
        """
        sql = MysqlAdapter.prepareSql(sql, params)
        MysqlAdapter.commonRunsql(sql)

        df_desc = pd.DataFrame({
            'trust_id': [params['TrustID']],
            'session_id': [params['SessionID']],
            'scenario_id': [params['ScenarioID']],
            'scenario_desc': [scenario_desc],
            'simulation_start_date': [params['SimulationStartDate']]
        })
        MysqlAdapter.dataframe_tosql(df_desc, "analysis_grouped_mob_scenario_desc")
    except Exception as e:
        print("[ERROR]:", e)


def get_scenario_desc_by_id(params):
    # 常规压测
    if params['SessionID'] == 'StressTest':
        sql = '''
            select ScenarioName from Analysis_Scenario where TrustID = @TrustID and ID = @ScenarioID
        '''
        sql = MysqlAdapter.prepareSql(sql, params)
        res = MysqlAdapter.commonExecuteGetData(sql)
        if res and res[0][0]:
            return res[0][0]
        else:
            print(f"未找到常规压测的情景名称, trust_id: {params['TrustID']}, scenario_id: {params['ScenarioID']}")
            return None

    # 敏感性分析
    if params['SessionID'] == 'Multiple_DefaultPrepayment':
        cdr = params['CDRValues'].split(',')[params['CDRStepID']]
        cpr = params['CPRValues'].split(',')[params['CPRStepID']]
        return f"违约倍数:{cdr}; 早偿倍数:{cpr};"

    # 多维度分析
    if params['SessionID'] == 'ProductDesign':
        plan_id = params['RevolvingPlanID']
        cdr = params['CDRValues'].split(',')[params['CDRStepID']]
        cpr = params['CPRValues'].split(',')[params['CPRStepID']]

        service_fee_rate_list = [] if len(params['ServiceFeeRateValues']) == 0 else params['ServiceFeeRateValues'].split(',')
        bond_extra_bp_list = [] if len(params['BondExtraBPValues']) == 0 else params['BondExtraBPValues'].split(',')
        bond_premium_list = [] if len(params['BondPremiumValues']) == 0 else params['BondPremiumValues'].split(',')
        discount_rate_list = [] if len(params['DiscountRateValues']) == 0 else params['DiscountRateValues'].split(',')
        service_fee_rate = '-' if len(service_fee_rate_list) == 0 else service_fee_rate_list[params['ServiceFeeRateStepID']]
        bond_extra_bp = '-' if len(bond_extra_bp_list) == 0 else bond_extra_bp_list[params['BondExtraBPStepID']]
        bond_premium = '-' if len(bond_premium_list) == 0 else bond_premium_list[params['BondPremiumStepID']]
        discount_rate = '-' if len(discount_rate_list) == 0 else discount_rate_list[params['DiscountRateStepID']]

        return (f'循环方案:{plan_id}; 覆盖率(%):{discount_rate}; 服务费率(%):{service_fee_rate}; ' +
            f'票面利率(%):{bond_extra_bp}; 次级溢价(%):{bond_premium}; 违约倍数:{cdr}; 早偿倍数:{cpr};')

    return None

def verify_grouped_mob_price(params, df_grouped_mob_price):
    print("==== 验证循环池分组MOB价格 ====")

    # 读取Analysis_GroupedMobPrice
    sql = """
        select * from Analysis_GroupedMobPrice where TrustID = @TrustID and SessionID = '@SessionID' and ScenarioID = @ScenarioID;
    """
    sql = MysqlAdapter.prepareSql(sql, params)
    df_grouped_mob_price_old = MysqlAdapter.exec_sql_to_df(sql)

    # 去掉Id列
    df_grouped_mob_price_old = df_grouped_mob_price_old.drop(columns=['Id'])

    # 比较新旧两种方法的的结果df的差异
    # 1. 确保具有相同的列名
    df_old = df_grouped_mob_price_old.copy()
    df_new = df_grouped_mob_price.copy()

    # 2. 统一排序，确保可比较
    df_old = df_old.sort_values(['TrustID', 'SessionID', 'ScenarioID', 'PayDate']).reset_index(drop=True)
    df_new = df_new.sort_values(['TrustID', 'SessionID', 'ScenarioID', 'PayDate']).reset_index(drop=True)

    # 3. 生成对比报告
    print(f"原始数据行数: {len(df_old)}, 新数据行数: {len(df_new)}")

    # 4. 检查列数是否相同
    if set(df_old.columns) != set(df_new.columns):
        print("列不一致：", set(df_old.columns).symmetric_difference(set(df_new.columns)))

    # 5. 对每个数值列执行相对误差分析（容忍小的浮点精度差异）
    tolerance = 1e-6  # 设定误差容忍度
    differences = 0
    col_differences = {}

    # 遍历所有M列进行详细比较
    for col in ['M0', 'M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M6plus']:
        if col in df_old.columns and col in df_new.columns:
            # 计算相对误差
            df_diff = pd.DataFrame({
                'old': df_old[col],
                'new': df_new[col],
                'abs_diff': abs(df_old[col] - df_new[col]),
                'rel_diff': abs(df_old[col] - df_new[col]) / (df_old[col].abs() + 1e-10)  # 避免除以零
            })

            # 找出差异超过容忍度的行
            significant_diff = df_diff[df_diff['rel_diff'] > tolerance]
            if len(significant_diff) > 0:
                differences += len(significant_diff)
                col_differences[col] = len(significant_diff)
                print(f"列 {col} 有 {len(significant_diff)} 行显著差异，最大相对误差: {significant_diff['rel_diff'].max():.6f}")

                # 显示最大差异的几行
                if len(significant_diff) > 0:
                    worst_rows = significant_diff.nlargest(3, 'rel_diff')
                    print("最大差异示例:")
                    for idx, row in worst_rows.iterrows():
                        print(f"  行 {idx}: 旧值={row['old']:.6f}, 新值={row['new']:.6f}, 差异={row['abs_diff']:.6f}")

    if differences == 0:
        print("两个DataFrame完全一致（在容差范围内）")
    else:
        print(f"总计 {differences} 个数据点有显著差异")

'''
    run_modes 必须传入字符串，如 '2' 或 '1,2,3'
    use_run_mask: 是否使用run_mask过滤，如果传入True，则仅返回run_mask=1的plan_id（对于模式3，是25种方案中均匀间隔选出的10种）
'''
def get_revolving_plans_by_run_mode(trust_id, run_modes, use_run_mask=False):
    # 如果run_modes不是字符串或者是空字符串，抛出异常
    if not isinstance(run_modes, str) or not run_modes:
        raise ValueError("run_modes must be a non-empty string")

    mask_filter = 'run_mask = 1' if use_run_mask else '1=1'

    # 如果方案包含3或4，则需要将方案1也包含进来
    if '3' in run_modes or '4' in run_modes:
        run_modes = '1,' + run_modes

    sql = f"""
        select distinct plan_id
        from trust_revolving_run_mode_plans
        where trust_id = {trust_id} and run_mode in ({run_modes}) and {mask_filter}
        order by plan_id;
    """
    res = MysqlAdapter.commonExecuteGetData(sql)

    return [row[0] for row in res]

def get_revolving_plans_by_trust(trust_id, use_run_mask=False, is_show=False):
    run_mode = 1

    # get run_mode
    sql = f"""
        select ItemValue
        from Analysis_SolutionExtension
        where SolutionID = {trust_id} and ItemCode = 'RevolvingPlanRunMode';
    """
    res = MysqlAdapter.commonExecuteGetData(sql)
    if res and res[0][0]:
        run_mode = int(res[0][0])

    run_modes = '3,5,6' if run_mode == 4 else str(run_mode)
    if is_show:
        use_run_mask = True if run_mode == 4 else False

    plans = get_revolving_plans_by_run_mode(trust_id, run_modes, use_run_mask=use_run_mask)

    return plans, run_mode
