#coding=utf-8

import sys
import urllib.parse
import traceback
from PythonFiles import MysqlAdapter2Java as MysqlAdapter, SQLParamsParser

def func(params):
    sql="""
        select CodeDictionaryId from Task_CodeDictionary as d
        inner join Task_CodeCategory as c on d.CodeCategoryId = c.CodeCategoryId and c.CategoryCode = 'ProcessTaskType'
        where d.CodeDictionaryCode = '@TaskCode' limit 1;
    """
    sql = MysqlAdapter.prepareSql(sql, params)
    # 如果获取不到，则设置为0
    res = MysqlAdapter.commonExecuteGetData(sql)
    params["ProcessTaskCodeId"] = res[0][0] if res and res[0][0] else 0

    sql="""
        delete from Task_SessionContext where SessionId = '@SessionId';

        INSERT INTO Task_SessionContext( SessionId ,VariableName ,VariableValue ,VariableDataType ,IsConstant ,IsKey ,KeyIndex)
        VALUES('@SessionId' ,'TaskCode' ,'@TaskCode' ,'varchar' ,1 ,0 ,0), ('@SessionId' ,'TrustID' ,@TrustID ,'varchar' ,1 ,0 ,0);

        INSERT  INTO Task_SessionContext( SessionId ,VariableName ,VariableValue ,VariableDataType ,IsConstant ,IsKey ,KeyIndex)
        SELECT  '@SessionId' ,VariableName ,VariableValue ,VariableDataType ,IsConstant ,IsKey ,KeyIndex
        FROM    Task_ProcessTaskContext
        WHERE   ProcessTaskCodeId = @ProcessTaskCodeId;
    """
    sql = MysqlAdapter.prepareSql(sql, params)
    MysqlAdapter.commonRunsql(sql)

    return 'success'


if __name__ == '__main__' :
    try:
        str_arg = ''''''
        if len(sys.argv) >= 2 and sys.argv[1] is not None:
            str_arg = sys.argv[1]

        arg = urllib.parse.unquote_plus(str_arg)
        params = SQLParamsParser.getParmas(arg)
        result = func(params)
        print('$OUTPUT' + result)
    except Exception as e:
        print('$ERROR', e, traceback.format_exc())
