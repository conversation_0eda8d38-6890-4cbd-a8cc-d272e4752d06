import json
import sys
import traceback
import urllib.parse

from PythonFiles import MysqlAdapter2Java as MysqlAdapter
from PythonFiles import SQLParamsParser


def func(params):
    sql='''
    	select TrustId, TrustCode, TrustName, IsMarketProduct
        from TrustManagement_Trust
        where TrustId <> 0
    '''
    sql = MysqlAdapter.prepareSql(sql,params)
    res=MysqlAdapter.commonExecute(sql)
    return res


if __name__ == '__main__':
    try:
        arg = urllib.parse.unquote_plus(sys.argv[1])
        arg = json.dumps(json.loads(arg)['Params'])
        params = SQLParamsParser.getParmas(arg)
        result = func(params)
        print("$output" + str(result))
    except Exception as e:
        print("$ERROR", e, traceback.format_exc())
