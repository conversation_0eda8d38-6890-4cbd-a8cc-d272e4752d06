import sys
import traceback
import urllib.parse
from datetime import datetime

import numpy as np
import pandas as pd
from PythonFiles import MysqlAdapter2Java as MysqlAdapter
from PythonFiles import RunBatchRecordHelper as rd
from PythonFiles import SQLParamsParser
from repurchase_data_prepare import get_calculation_input
from repurchase_fin_logic import FinancialEngine
from supporting_functions import get_is_calculate_repurchase_price, get_repurchase_price_basis

# 定义需要计算的列对
PRICING_PAIRS = [
    ('price_m0', 'm0', 'pricing_m0', 'interest_m0'),
    ('price_m1', 'm1', 'pricing_m1', 'interest_m1'),
    ('price_m2', 'm2', 'pricing_m2', 'interest_m2'),
    ('price_m3', 'm3', 'pricing_m3', 'interest_m3'),
    ('price_m4', 'm4', 'pricing_m4', 'interest_m4'),
    ('price_m5', 'm5', 'pricing_m5', 'interest_m5'),
    ('price_m6', 'm6', 'pricing_m6', 'interest_m6'),
    ('price_m6plus', 'm6plus', 'pricing_m6plus', 'interest_m6plus')
]

INTEREST_COLS = ['interest_m0', 'interest_m1', 'interest_m2', 'interest_m3', 
                 'interest_m4', 'interest_m5', 'interest_m6', 'interest_m6plus']

BALANCE_COLS = ['m0','m1','m2','m3','m4','m5','m6','m6plus']


class DataProcessor:
    """数据处理类"""

    @staticmethod
    def get_trust_name(trust_id):
        """获取信托名称"""
        sql = '''
            select TrustName from Analysis_Trust where TrustId = @TrustID;
        '''
        sql = MysqlAdapter.prepareSql(sql, {'TrustID': trust_id})
        res = MysqlAdapter.exec_sql_to_df(sql)
        return res['TrustName'].values[0] if not res.empty else None

    @staticmethod
    def get_stress_test_id(params):
        """获取压力测试的场景id和sessionid"""
        sql = '''
            select ScenarioID as scenario_id, TaskSessionID2 as task_session_id, null as baseline_scenario
            from Analysis_ScenarioSessions
            where TrustID = @TrustID and ScenarioID = @ScenarioID and TaskSessionID2 is not null
        '''
        sql = MysqlAdapter.prepareSql(sql, params)
        df_session_id = MysqlAdapter.exec_sql_to_df(sql)

        sql = '''
            select trust_id, session_id, scenario_id, scenario_desc
            from analysis_grouped_mob_scenario_desc
            where trust_id = @TrustID and session_id = '@Category' and scenario_id = '@ScenarioID';
        '''
        sql = MysqlAdapter.prepareSql(sql, params)
        df_scenario_desc = MysqlAdapter.exec_sql_to_df(sql)

        return df_scenario_desc.merge(df_session_id, on=['scenario_id'], how='inner')

    @staticmethod
    def get_sensitivity_analysis_multiple_ids(params):
        """获取倍数敏感性分析的场景id和sessionid"""
        sql = '''
            select distinct Dimension1, Dimension2, TaskSessionID as task_session_id, null as baseline_scenario
            from Analysis_SensitivityAnalysisMultipleResult
            where TrustID = @TrustID and SensitivityMode = '@Category'
            order by Dimension1 , Dimension2
        '''
        sql = MysqlAdapter.prepareSql(sql, params)
        res = MysqlAdapter.exec_sql_to_df(sql)
        # 将Dimension1转换为数值类型并按大小排序后赋予索引
        temp_series = pd.to_numeric(res['Dimension1'], errors='coerce')
        res['Dimension1_index'] = pd.Series(temp_series).rank(method='dense').astype(int) - 1
        temp_series_dim2 = pd.to_numeric(res['Dimension2'], errors='coerce')
        res['Dimension2_index'] = pd.Series(temp_series_dim2).rank(method='dense').astype(int) - 1
        # 计算scenario_id
        res['scenario_id'] = res['Dimension2_index'] * 1000 + res['Dimension1_index']
        # 删除中间列
        df_session_id = res[['scenario_id', 'task_session_id', 'baseline_scenario']]

        sql = '''
            select trust_id, session_id, scenario_id, scenario_desc
            from analysis_grouped_mob_scenario_desc
            where trust_id = @TrustID and session_id = '@Category';
        '''
        sql = MysqlAdapter.prepareSql(sql, params)
        df_scenario_desc = MysqlAdapter.exec_sql_to_df(sql)

        return df_scenario_desc.merge(df_session_id, on=['scenario_id'], how='inner')

    @staticmethod
    def get_product_design_ids(params):
        """获取多维度产品设计的场景id和sessionid"""
        sql = '''
            select scenario_id, task_session_id, baseline_scenario * 1 as baseline_scenario
            from product_design_risk_transfer_report
            where trust_id = @TrustID;
        '''
        sql = MysqlAdapter.prepareSql(sql, params)
        df_session_id = MysqlAdapter.exec_sql_to_df(sql)

        sql = '''
            select trust_id, session_id, scenario_id, scenario_desc
            from analysis_grouped_mob_scenario_desc
            where trust_id = @TrustID and session_id = '@Category';
        '''
        sql = MysqlAdapter.prepareSql(sql, params)
        df_scenario_desc = MysqlAdapter.exec_sql_to_df(sql)

        return df_scenario_desc.merge(df_session_id, on=['scenario_id'], how='inner')

    @staticmethod
    def delete_result_by_scenario(params):
        """删除指标数据"""
        sql = '''
            delete from analysis_grouped_mob_price_summary
            where session_id = '@Category' and trust_id = @TrustID and scenario_id = @ScenarioID;

            delete from analysis_grouped_mob_price_pi_summary
            where session_id = '@Category' and trust_id = @TrustID and scenario_id = @ScenarioID;

            delete from repurchase_metric_adjustment
            where category = '@Category' and trust_id = @TrustID 
                and scenario_id = @ScenarioID and simulation_start_date = '@RunDate';

            delete from repurchase_adjustment
            where category = '@Category' and trust_id = @TrustID 
                and scenario_id = @ScenarioID and simulation_start_date = '@RunDate';
        '''
        sql = MysqlAdapter.prepareSql(sql, params)
        MysqlAdapter.commonRunsql(sql)

    @staticmethod
    def delete_result(params):
        """删除指标数据"""
        sql = '''
            delete from analysis_grouped_mob_price_summary
            where session_id = '@Category' and trust_id = @TrustID;
            
            delete from analysis_grouped_mob_price_pi_summary
            where session_id = '@Category' and trust_id = @TrustID;

            delete from repurchase_metric_adjustment
            where category = '@Category' and trust_id = @TrustID 
                and simulation_start_date = '@RunDate';

            delete from repurchase_adjustment
            where category = '@Category' and trust_id = @TrustID 
                and simulation_start_date = '@RunDate';
        '''
        sql = MysqlAdapter.prepareSql(sql, params)
        MysqlAdapter.commonRunsql(sql)


def calculate_metrics_by_scenario(params, df_single, calculation_input, rates):
    task_session_id = df_single.at[0, 'task_session_id']
    scenario_desc = df_single.at[0, 'scenario_desc']
    print(f"开始计算场景名称: {scenario_desc}, session_id: {task_session_id}")

    # 计算关键指标
    engine = FinancialEngine()
    key_metrics = engine.calculate_all_key_metrics(rates, input=calculation_input)
    
    # 准备要保存的数据
    metrics_data = {
        'trust_id': params['TrustID'],
        'category': params['Category'],
        'scenario_id': df_single.at[0, 'scenario_id'],
        'scenario_desc': scenario_desc,
        'baseline_scenario': df_single.at[0, 'baseline_scenario'],
        'task_session_id': task_session_id,
        'repurchase_calculation_date': df_single.at[0, 'repurchase_calculation_date'],
        'simulation_start_date': df_single.at[0, 'simulation_start_date'],
        'repurchase_price': key_metrics.repurchase_price,
        'payment_gap': key_metrics.payment_gap,
        'secondary_irr': key_metrics.secondary_irr,
        'secondary_allocation': key_metrics.secondary_allocation,
        'var_comp_ratio_incl_excess': key_metrics.var_comp_ratio_incl_excess,
        'asset_service_fee': key_metrics.asset_service_fee,
        'full_excess_service_fee': key_metrics.full_excess_service_fee,
        'full_excess_service_fee_ratio': key_metrics.full_excess_service_fee_ratio,
        'm0_rate': rates[0] * 100,
        'm1_rate': rates[1] * 100,
        'm2_rate': rates[2] * 100,
        'm3_rate': rates[3] * 100,
        'm4_rate': rates[4] * 100,
        'm5_rate': rates[5] * 100,
        'm6_rate': rates[6] * 100,
        'm6plus_rate': rates[7] * 100,
        'step_id': 1
    }

    return metrics_data

def get_repurchase_adjustment(params, df_single, calculation_input, rates):
    """获取回购折价率调整前的数据"""
    asset_categories = [
        'M0', 'M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M6+'
    ]

    repurchase_list = []
    for index, cat in enumerate(asset_categories):
        repurchase_list.append({
            'trust_id': df_single.at[0, 'trust_id'],
            'category': params['Category'],
            'repurchase_price_basis': params['repurchase_price_basis'],
            'scenario_id': df_single.at[0, 'scenario_id'],
            'simulation_start_date': df_single.at[0, 'simulation_start_date'],
            'task_session_id': df_single.at[0, 'task_session_id'],
            'asset_category': cat,
            'balance': calculation_input.balances[index],
            'rate': rates[index] * 100,
            'adjustment_price': None,
            'adjustment_rate': None,
            'forecasted_interest': df_single.at[0, INTEREST_COLS[index]]
        })
    return repurchase_list

def get_repurchase_origin_rates(params, repurchase_list):
    if repurchase_list:
        sql = f'''
            SELECT 'M0' AS asset_category, M0 AS origin_rate FROM Analysis_BondRepurchaseSettingExt where TrustID = {params['TrustID']}
            UNION ALL
            SELECT 'M1' AS asset_category, M1 AS origin_rate FROM Analysis_BondRepurchaseSettingExt where TrustID = {params['TrustID']}
            UNION ALL
            SELECT 'M2' AS asset_category, M2 AS origin_rate FROM Analysis_BondRepurchaseSettingExt where TrustID = {params['TrustID']}
            UNION ALL
            SELECT 'M3' AS asset_category, M3 AS origin_rate FROM Analysis_BondRepurchaseSettingExt where TrustID = {params['TrustID']}
            UNION ALL
            SELECT 'M4' AS asset_category, M4 AS origin_rate FROM Analysis_BondRepurchaseSettingExt where TrustID = {params['TrustID']}
            UNION ALL
            SELECT 'M5' AS asset_category, M5 AS origin_rate FROM Analysis_BondRepurchaseSettingExt where TrustID = {params['TrustID']}
            UNION ALL
            SELECT 'M6' AS asset_category, M6 AS origin_rate FROM Analysis_BondRepurchaseSettingExt where TrustID = {params['TrustID']}
            UNION ALL
            SELECT 'M6+' AS asset_category, M6plus AS origin_rate FROM Analysis_BondRepurchaseSettingExt where TrustID = {params['TrustID']}
        '''
        df_origin_rate = MysqlAdapter.exec_sql_to_df(sql)
        df_origin_rate['origin_rate'] = df_origin_rate['origin_rate'].astype(float)
        df_repurchase = pd.DataFrame(repurchase_list)
        df_repurchase['price'] = df_repurchase['balance'] * df_repurchase['rate'] / 100
        df_repurchase = df_repurchase.merge(df_origin_rate, on='asset_category', how='left')
        df_repurchase['origin_price'] = df_repurchase['balance'] * df_repurchase['origin_rate'] / 100
        df_repurchase['origin_price_pi'] = df_repurchase['origin_price'] + df_repurchase['forecasted_interest']
        with np.errstate(divide='ignore', invalid='ignore'):
            df_repurchase['origin_rate_pi'] = np.where(
                (df_repurchase['balance'].notna()) & (df_repurchase['balance'] != 0) & (df_repurchase['origin_price_pi'].notna()),
                df_repurchase['origin_price_pi'] / df_repurchase['balance'] * 100,
                np.nan
            )
        return df_repurchase

def get_repurchase_calculation_date(params):
    # 获取回购起算日
    params['term'] = 0
    if params['repurchase_price_basis'] in ("PoolBalance_Opening", "PoolBalancePI_Opening"):
        params['term'] = 1
    sql = '''
        select SessionId as task_session_id, ItemValue as pay_date
        from Task_SessionContextResult 
        where SessionId = '@task_session_id' and ItemName = '计算日'
            and PeriodsId = (
                select if(max(PeriodsId) - @term < 0, 0, max(PeriodsId) - @term) 
                from Task_SessionContextResult
                where SessionId = '@task_session_id' and ItemName = '计算日'
            )
    '''
    sql = MysqlAdapter.prepareSql(sql, params)
    df_calculation_date = MysqlAdapter.exec_sql_to_df(sql)
    if df_calculation_date.empty:
        raise Exception("未找到回购起算日，无法进行回购价格计算")
    return df_calculation_date

def set_forecasted_interest(params, df_summary_single):
    # 获取预测利息
    repurchase_calculation_date = df_summary_single.at[0, 'repurchase_calculation_date']
    total_interest, repurchase_price_basis = None, '本金余额'
    if params['repurchase_price_basis'].startswith("PoolBalancePI"):
        repurchase_price_basis = '本息余额'
        sql = f'''
            select sum(Interest) as interest from Analysis_StressedCashflowDetails
            where TrustID = {params['TrustID']} and SessionID = '{params['Category']}' 
                and ScenarioID = {params['scenario_id']} and RevolvingPurchaseID = 0
                and PayDate > '{repurchase_calculation_date}'
        '''
        res = MysqlAdapter.commonExecuteGetData(sql)
        total_interest = res[0][0] if res and res[0][0] else 0

        # 从 df_summary_single 的第一行提取 m0~m6plus 字段，转换为纵向 pd.Series（整数索引 0..7）
        # 提取第一行对应列并转为 float，确保索引为位置索引以便后续通过 interests[0]..访问
        balances = df_summary_single.loc[:, BALANCE_COLS].iloc[0].astype(float)
        balances.reset_index(drop=True, inplace=True)
        interests = (balances / balances.sum() * total_interest).fillna(0)

    for index, value in enumerate(INTEREST_COLS):
        df_summary_single[value] = interests[index] if repurchase_price_basis == '本息余额' else None

    return df_summary_single

def cal_discount_rate_by_scenario(params, df_sessions, balances):
    # 获取回购起算日
    df_calculation_date = get_repurchase_calculation_date(params)

    df_calculation_date = df_calculation_date.merge(df_sessions, on='task_session_id', how='inner')

    sql = '''
        select e.trust_id, e.session_id, e.scenario_id, e.pay_date, e.group_id, s.simulation_start_date,
            e.m0, e.m1, e.m2, e.m3, e.m4, e.m5, e.m6, e.m6plus, 
            e.price_m0, e.price_m1, e.price_m2, e.price_m3, e.price_m4, e.price_m5, e.price_m6, e.price_m6plus
        from analysis_grouped_mob_price_expanded as e
        inner join analysis_grouped_mob_scenario_desc as s on e.trust_id = s.trust_id 
            and e.session_id = s.session_id and e.scenario_id = s.scenario_id
        where e.trust_id = @TrustID and e.session_id = '@Category' and e.scenario_id = @scenario_id;
    '''
    sql = MysqlAdapter.prepareSql(sql, params)
    df_price = MysqlAdapter.exec_sql_to_df(sql)
    
    if len(df_price) == 0:
        raise Exception(f"未找到产品ID {params['TrustID']}，测算类型 {params['Category']}，\
                        场景ID {params['scenario_id']} 的分组数据，无法进行回购价格计算")

    df_price = df_price.merge(df_calculation_date, on=['trust_id','session_id','scenario_id','pay_date'], how='inner')

    # 汇总回购价格
    df_summary_single = df_price.groupby(['trust_id','session_id','scenario_id','scenario_desc', \
                                'simulation_start_date','pay_date','task_session_id', \
                                'baseline_scenario'], dropna=False).sum().reset_index()
    df_summary_single.rename(columns={'pay_date': 'repurchase_calculation_date'}, inplace=True)

    # 手动释放大DataFrame的内存
    del df_price
    del df_calculation_date

    # 计算回购折价率
    for index, (price_col, m_col, pricing_col, _) in enumerate(PRICING_PAIRS):
        if price_col in df_summary_single.columns and m_col in df_summary_single.columns:
            df_summary_single[pricing_col] = (df_summary_single[price_col] / df_summary_single[m_col]).round(6)
            # 替换本金余额为现金流模型里值
            df_summary_single[m_col] = balances[index]
            df_summary_single[price_col] = df_summary_single[m_col] * df_summary_single[pricing_col]

    df_summary_single = set_forecasted_interest(params, df_summary_single)

    return df_summary_single

def get_discount_rates_as_series(df_summary_single):
    # 获取m0 ~ m6+回购折价率
    rates_cols = [
        'pricing_m0', 'pricing_m1', 'pricing_m2', 'pricing_m3', 'pricing_m4',
        'pricing_m5', 'pricing_m6', 'pricing_m6plus'
    ]
    rates = df_summary_single.loc[:, rates_cols].iloc[0].astype(float)
    rates = rates.reset_index(drop=True)
    return rates

def cal_price_summary(params: dict, df_sessions: pd.DataFrame):
    if df_sessions.empty:
        return pd.DataFrame()
    
    task_session_ids = df_sessions['task_session_id'].astype(str).tolist()
    if len(task_session_ids) == 0:
        return pd.DataFrame()
    
    scenario_ids = df_sessions['scenario_id'].astype(str).tolist()

    params['repurchase_price_basis'] = get_repurchase_price_basis(params['TrustID'])

    df_summary = pd.DataFrame()
    metrics_data_list = []
    repurchase_list_total = []
    for i in range(len(task_session_ids)):
        params['task_session_id'] = task_session_ids[i]
        params['scenario_id'] = scenario_ids[i]
        calculation_input = get_calculation_input(params['TrustID'], params['task_session_id'], params['Category'], 1)
        # 回购折价率提取
        df_summary_single= cal_discount_rate_by_scenario(params, df_sessions, calculation_input.balances)
        df_summary = pd.concat([df_summary, df_summary_single], ignore_index=True)

        # 调整前指标计算 repurchase_metric_adjustment
        rates = get_discount_rates_as_series(df_summary_single)
        metrics_data = calculate_metrics_by_scenario(params, df_summary_single, calculation_input, rates)
        metrics_data_list.append(metrics_data)
        # repurchase_adjustment
        repurchase_list = get_repurchase_adjustment(params, df_summary_single, calculation_input, rates)
        repurchase_list_total.extend(repurchase_list)

        # 在循环中释放临时数据
        del df_summary_single

    # 计算本息余额回购相关信息
    if params['repurchase_price_basis'] is not None and params['repurchase_price_basis'].startswith("PoolBalancePI"):
        df_summary_pi = df_summary.copy(deep=True)
        for price_col, m_col, pricing_col, int_col in PRICING_PAIRS:
            if price_col in df_summary.columns and m_col in df_summary.columns:
                df_summary_pi[pricing_col] = ((df_summary_pi[price_col] + df_summary_pi[int_col]) / df_summary_pi[m_col]).round(6)
                df_summary_pi[price_col] = df_summary_pi[price_col] + df_summary_pi[int_col]
        MysqlAdapter.dataframe_tosql(df_summary_pi, "analysis_grouped_mob_price_pi_summary")

    df_summary.drop(columns=INTEREST_COLS, inplace=True)
    MysqlAdapter.dataframe_tosql(df_summary, "analysis_grouped_mob_price_summary")

    # 保存指标和调整前的数据
    if metrics_data_list:
        df_metrics = pd.DataFrame(metrics_data_list)
        MysqlAdapter.dataframe_tosql(df_metrics, "repurchase_metric_adjustment")

    if repurchase_list_total:
        # 获取压测时点回购折价率信息 
        df_repurchase = get_repurchase_origin_rates(params, repurchase_list_total)
        MysqlAdapter.dataframe_tosql(df_repurchase, "repurchase_adjustment")

def mob_price_summary(params):
    """主函数"""
    # 解析参数
    trust_id = params['TrustID']

    # 检查是否需要计算回购价格
    is_calculate_repurchase_price = get_is_calculate_repurchase_price(trust_id)
    if is_calculate_repurchase_price != 1 or params['ExecutionMode'] != '01':
        return '不计算回购价格，不进行处理'

    trust_name = DataProcessor.get_trust_name(trust_id)

    if trust_name is None or '放款池' in trust_name:
        return '放款池产品，不进行处理'

    df_sessions = pd.DataFrame()
    # 根据测算类型获取sessionids
    if params['Category'] == 'StressTest':
        # 获取sessionid
        df_sessions = DataProcessor.get_stress_test_id(params)
        # 删除指标数据
        DataProcessor.delete_result_by_scenario(params)
    elif params['Category'] == 'SensitivityAnalysisMultiple': # 倍数敏感性分析
        # 将category设置为Multiple_DefaultPrepayment，因为后续数据表中存的是Multiple_DefaultPrepayment
        params['Category'] = 'Multiple_DefaultPrepayment'
        # 获取场景id和sessionid
        df_sessions = DataProcessor.get_sensitivity_analysis_multiple_ids(params)
        # 删除指标数据
        DataProcessor.delete_result(params)
    elif params['Category'] == 'ProductDesign': # 多维度产品设计
        df_sessions = DataProcessor.get_product_design_ids(params)
        # 删除指标数据
        DataProcessor.delete_result(params)

    cal_price_summary(params, df_sessions)

    return '计算完成'


if __name__ == '__main__':
    try:
        '''
            参数示例：
            {"TrustID": 111, "Category": "StressTest", "RunDate": "2025-03-25", "ExecutionMode": "01"}
        '''
        str_arg = ''''''
        if len(sys.argv) >= 2 and sys.argv[1] is not None:
            str_arg = sys.argv[1]
        else:
            raise Exception("参数错误")

        arg = urllib.parse.unquote_plus(str_arg)
        params = SQLParamsParser.getParmas(arg)
        if 'RunDate' not in params.keys() or params["RunDate"] == '':
            params["RunDate"] = datetime.now().strftime('%Y-%m-%d')
        record = rd.RecordClass(params["RunDate"], params["Category"])
        try:
            result = mob_price_summary(params)
            record.update_record(params["TrustID"], 3, "回购折价率提取完成")
            print('$OUTPUT' + result)
        except Exception as e:
            error_info = traceback.format_exc()
            print('$ERROR', e, error_info.strip().replace("'",'"').replace(";",''))
            record.update_record(params["TrustID"], -1, error_info.strip().replace("'",'"').replace(";",''))
    except Exception as e:
        print("$ERROR", e, traceback.format_exc())
