import json
import sys
import traceback
import urllib.parse
from PythonFiles import SQLParamsParser
from PythonFiles import MysqlAdapter2Java as MysqlAdapter


def func(params):
    sql = '''
        delete from Analysis_BondRepurchaseSettingExt where TrustID=@TrustID
    '''
    sql = MysqlAdapter.prepareSql(sql, params)
    MysqlAdapter.commonExecute(sql)

    enm=['CallPriceActual','M0','M1','M2','M3','M4','M5','M6','M6plus','TriggerNextMonth']
    for i in enm:
        if i not in params:
            params[i]=None
    params["plusM6"] = params["M6plus"]

    sql = '''
        insert into Analysis_BondRepurchaseSettingExt (
            TrustID, CalculateBasis, DiscountRate, SpecifiedCallDiscount, CallAvailableAmount,
            CallPriceActual, M0, M1, M2, M3, M4, M5, M6, M6plus, TriggerNextMonth,
            is_calculate_repurchase_rate, is_calculate_repurchase_date
        )
        values (@TrustID, '@CalculateBasis', '@DiscountRate', @SpecifiedCallDiscount,
        '@CallAvailableAmount', @CallPriceActual,
        @M0, @M1, @M2, @M3, @M4, @M5, @M6, @plusM6,
        @TriggerNextMonth, @IsCalculateRepurchaseRate, @IsCalculateRepurchaseDate)
    '''
    sql = MysqlAdapter.prepareSql(sql, params)
    res = MysqlAdapter.commonExecute(sql)
    return res


if __name__ == '__main__':
    try:
        arg = json.loads(urllib.parse.unquote_plus(sys.argv[1]))
        arg = json.dumps(arg['Params'])
        params = SQLParamsParser.getParmas(arg)
        result = func(params)
        print('$OUTPUT' + result)
    except Exception as e:
        print("$ERROR", e, traceback.format_exc())
