<template>
    <div class="data-grid-parent--fixed">
        <div class="data-grid data-grid--fixed">
            <tool-bar title="任务状态查看">
                <template v-slot:tool-right>
                    <label for="" class="mr-10">日期选择</label>
                    <el-date-picker v-model="where.executionDate.value"  @change="getTableData" format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker>
                </template>
            </tool-bar>
            <el-table :data="table" v-loading="loading" size="mini" height="auto">
                <el-table-column prop="assetSource" width="140">
                    <template v-slot:header>
                        资产来源
                        <SearchPanel field="assetSource" dataType="string" @onFilter="onFilter" />
                    </template>
                </el-table-column>
                <el-table-column prop="category" width="140">
                    <template v-slot:header>
                        业务类别
                        <SearchPanel field="category" dataType="string" @onFilter="onFilter" />
                    </template>
                </el-table-column>
                <el-table-column prop="messageType" width="180">
                    <template v-slot:header>
                        任务类别
                        <SearchPanel field="messageType" dataType="string" @onFilter="onFilter" />
                    </template>
                </el-table-column>
                <el-table-column prop="taskName" label="任务名称" width="250">
                    <template v-slot:header>
                        任务名称
                        <SearchPanel field="taskName" dataType="string" @onFilter="onFilter" />
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="任务状态" width="120">
                    <template v-slot:header>
                        任务状态
                        <SearchPanel field="status" dataType="string" :options="Object.entries(statusMap).map(([key, value]) => ({ label: value, value: key }))" @onFilter="onFilter" />
                    </template>
                    <template slot-scope="scope">
                        <span :class="getStatusClass(getStatus(scope.row.status))">
                            <i :class="getStatusIcon(getStatus(scope.row.status))"></i>
                            {{ getStatus(scope.row.status) }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column prop="executionDate" label="数据日期" width="140"></el-table-column>
                <el-table-column prop="startedAt" label="开始时间" width="170"></el-table-column>
                <el-table-column prop="completedAt" label="结束时间" width="170"></el-table-column>
                <el-table-column prop="remark" label="备注" min-width="400"></el-table-column>
            </el-table>
            <div class="grid-pager-control">
                <el-pagination background @size-change="pageSizeChange" @current-change="pageIndexChange"
                    :current-page.sync="current" :page-size.sync="size" :page-sizes="[20, 50, 100]" :total="total"
                    ref="page" layout="total, sizes, prev, pager, next, jumper"></el-pagination>
            </div>
        </div>
    </div>
</template>

<script>
import ToolBar from '@/components/table/ToolBar.vue';
import SearchPanel from '@/components/table/SearchPanel.vue';
import dynamicTab from '@/mixins/dynamicTab.js';
import { taskInstanceReset, taskInstancePage } from '@/api/Api'

export default {
    components: { ToolBar, SearchPanel },
    mixins: [dynamicTab],
    data() {
        return {
            table: [],
            current: 1,
            size: 20,
            total: 0,
            loading: false,
            where: {
                executionDate: {op: 'eq', value: new Date(new Date().setDate(new Date().getDate() - 1)).Format('yyyy-MM-dd') }
            },
            statusMap: {
                PENDING: '未执行',
                RUNNING: '运行中',
                COMPLETED: '成功',
                FAILED: '失败',
                DEFAULT: '未知' // 用于处理未知状态
            }
        }
    },
    created() {
        this.newTab({
            id: 'TaskStatus',
            name: 'TaskStatus',
            title: '任务状态查看'
        })
        this.getTableData()
    },
    methods: {
        resetTask(row) {
            this.$confirm('该操作将重置当前任务状态，是否继续?', '提示', {
                confirmButtonText: '是',
                cancelButtonText: '否',
                type: 'warning'
            }).then(() => {
                taskInstanceReset({
                    instanceId: row.instanceId
                }).then(res => {
                    this.$message.success('重置成功');
                    this.getTableData();
                })  
            })          
        },
        getStatus(status) {
            return this.statusMap[status] || this.statusMap.DEFAULT;
        },
        onFilter(filter) {
            const type = {
                '>': 'gt',
                '<': 'lt',
                '=': 'eq',
                '>=': 'ge',
                '<=': 'le',
                '!=': 'ne',
            }
            // console.log(type[filter.type], filter.type)
            this.where[filter.field] = {
                op: type[filter.type] ? type[filter.type] : filter.type,
                value: filter.values
            }
            if(filter.values == '') {
                delete this.where[filter.field];
            }
            this.current = 1;
            this.getTableData();
        },
        getTableData() {
            this.loading = true;
            taskInstancePage({
                pageNum: this.current,
                pageSize: this.size,
                filters: this.where
            }).then(res => {
                this.table = res.result.records;
                this.total = res.result.total;
                this.loading = false;
                console.log(res)
            })
        },
        pageSizeChange(size) {
            this.size = size;
            this.current = 1;
            this.getTableData();
        },
        pageIndexChange(index) {
            this.current = index;
            this.getTableData();
        },
        getStatusClass(status) {
            switch (status) {
                case '未执行':
                    return 'status-pending';
                case '运行中':
                    return 'status-running';
                case '成功':
                    return 'status-completed';
                case '失败':
                    return 'status-failed';
                default:
                    return '';
            }
        },
        getStatusIcon(status) {
            switch (status) {
                case '未执行':
                    return 'el-icon-time';
                case '运行中':
                    return 'el-icon-loading';
                case '成功':
                    return 'el-icon-circle-check';
                case '失败':
                    return 'el-icon-circle-close';
                default:
                    return '';
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.status-pending {
    color: $--color-gray;
}

.status-running {
    color: $--color-blue;
}

.status-completed {
    color: $--color-green;
}

.status-failed {
    color: $--color-danger;
}
</style>