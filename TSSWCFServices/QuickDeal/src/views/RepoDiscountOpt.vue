<template>
    <div class="repo-discount-opt">
        <base-box title="回购折价率调优">
            <el-form inline label-width="100px">
                <el-form-item label="专项计划" required>
                    <el-select v-model="form.trustId" filterable @change="getDateOptions" style="width: 480px;"
                        popper-class="max-height-select-dropdown">
                        <el-option v-for="item in trustOptions" :key="item.TrustId"
                            :label="item.TrustCode + '_' + item.TrustName + '( ID: ' + item.TrustId + ' )'"
                            :value="item.TrustId"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="测算日期" required>
                    <el-date-picker v-model="form.calcDate" :picker-options="pickerOptions" type="date"
                        value-format="yyyy-MM-dd" @change="getScenarioOptions"></el-date-picker>
                </el-form-item>
                <el-form-item label="数据日期">
                    <el-input :value="dataDate" disabled></el-input>
                </el-form-item>
                <div>
                    <el-form-item label="测算情景" required>
                        <el-select v-model="form.scenarioId" filterable style="width: 760px;"
                            popper-class="max-height-select-dropdown" @change="getBeforeAdjustment">
                            <el-option v-for="item in scenarioOptions" :key="item.task_session_id"
                                :label="item.scenario_desc" :value="item.task_session_id"></el-option>
                        </el-select>
                    </el-form-item>
                </div>
                <div>
                    <el-form-item label="回购价格基准">
                        <el-input v-model="form.basis" disabled></el-input>
                    </el-form-item>
                </div>
                <div>
                    <el-form-item label="业务要求配置">
                        <table class="yq-table">
                            <tr>
                                <th>
                                    次级IRR(%)
                                </th>
                                <td>
                                    下限：{{ form.irr_down }}
                                </td>
                                <td>
                                    上限：{{ form.irr_up }}
                                </td>
                            </tr>
                            <tr>
                                <th>
                                    可变报酬占比(含超额)(%)
                                </th>
                                <td>
                                    下限：{{ form.vc_proportion_down }}
                                </td>
                                <td>
                                    上限：{{ form.vc_proportion_up }}
                                </td>
                            </tr>
                            <tr>
                                <th>
                                    超额占比(%)
                                </th>
                                <td>
                                    下限：{{ form.service_rate_down }}
                                </td>
                                <td>
                                    上限：{{ form.service_rate_up }}
                                </td>
                            </tr>
                        </table>
                    </el-form-item>
                </div>
                <el-form-item label="回购起算日">
                    <el-input v-model="beforeAdjustment.repurchase_calculation_date" disabled></el-input>
                </el-form-item>
                <el-form-item label="">
                    <el-button type="primary" @click="runTask"
                        :disabled="loading || form.calcDate < today">运行调节计算</el-button>
                </el-form-item>
            </el-form>
            <div style="display: flex;margin-bottom: 10px;margin-top: 10px;">
                <div class="mr-10">
                    <div style="background-color: #ffeee5; padding: 6px 10px;width: 100px;text-align: right;">调整前:
                    </div>
                </div>
                <el-form inline>
                    <el-form-item label="清仓回购价格">
                        <el-tooltip placement="bottom">
                            <div slot="content">
                                <div>清仓回购价格 = SUM("M1-M6+回购折价率"x对应回购起算日的"M1-M6+本金余额")
                                </div>
                            </div>
                            <span class="mr-5">
                                <i class="el-icon-info help"></i>
                            </span>
                        </el-tooltip>
                        <el-input :value="toMoney(beforeAdjustment.repurchase_price)" :disabled="true"
                            style="width: 130px"></el-input>
                    </el-form-item>
                    <el-form-item label="兑付缺口">
                        <el-tooltip placement="bottom">
                            <div slot="content">
                                <div>兑付缺口 = 现金流瀑布最后一期[(本金应付+利息应付) - (本金实付+利息实付)]
                                </div>
                            </div>
                            <span class="mr-5">
                                <i class="el-icon-info help"></i>
                            </span>
                        </el-tooltip>
                        <el-input :value="toMoney(beforeAdjustment.payment_gap)" :disabled="true"
                            style="width: 130px"></el-input>
                    </el-form-item>
                    <el-form-item label="次级IRR(%)">
                        <el-tooltip placement="bottom">
                            <div slot="content">
                                <div>运用XIRR函数迭代求解使(次级本金+利息)现值总和为零的年化折现率，即次级投资人实际收益率
                                </div>
                            </div>
                            <span class="mr-5">
                                <i class="el-icon-info help"></i>
                            </span>
                        </el-tooltip>
                        <el-input v-model="beforeAdjustment.secondary_irr" :disabled="true"
                            style="width: 130px"></el-input>
                    </el-form-item>
                    <el-form-item label="可变报酬占比(含超额)(%)">
                        <el-tooltip placement="bottom">
                            <div slot="content">
                                <div>
                                    SPV内原始权益人收益 = “浮动服务费 ” + 自持部分利息分配 + 清仓回购“超额报酬”；<br />
                                    SPV总收益 = 资产包总现金流入 - 券端本金分配 - 循环购买支出；<br />
                                    VR（可变报酬） = SPV内原始权益人收益现值/SPV总收益现值。<br />
                                </div>
                            </div>
                            <span class="mr-5">
                                <i class="el-icon-info help"></i>
                            </span>
                        </el-tooltip>
                        <el-input v-model="beforeAdjustment.var_comp_ratio_incl_excess" :disabled="true"
                            style="width: 130px"></el-input>
                    </el-form-item>
                    <el-form-item label="超额报酬占比(%)">
                        <el-tooltip placement="bottom">
                            <div slot="content">
                                <div>
                                    超额占比(%) = 超额报酬实付/基础回购价格
                                </div>
                            </div>
                            <span class="mr-5">
                                <i class="el-icon-info help"></i>
                            </span>
                        </el-tooltip>
                        <el-input v-model="beforeAdjustment.full_excess_service_fee_ratio" :disabled="true"
                            style="width: 130px"></el-input>
                    </el-form-item>
                </el-form>
            </div>
            <div style="display: flex;margin-bottom: 10px;margin-top: 10px;">
                <div class="mr-10">
                    <div style="background-color: #e5efff; padding: 6px 10px;width: 100px;text-align: right;">调整后:
                    </div>
                </div>
                <el-form inline>
                    <el-form-item label="清仓回购价格">
                        <el-tooltip placement="bottom">
                            <div slot="content">
                                <div>清仓回购价格 = SUM("M1-M6+回购折价率"x对应回购起算日的"M1-M6+本金余额")
                                </div>
                            </div>
                            <span class="mr-5">
                                <i class="el-icon-info help"></i>
                            </span>
                        </el-tooltip>
                        <el-input :value="toMoney(afterAdjustment.repurchase_price)" :disabled="true"
                            style="width: 130px"></el-input>
                    </el-form-item>
                    <el-form-item label="兑付缺口">
                        <el-tooltip placement="bottom">
                            <div slot="content">
                                <div>兑付缺口 = 现金流瀑布最后一期[(本金应付+利息应付) - (本金实付+利息实付)]
                                </div>
                            </div>
                            <span class="mr-5">
                                <i class="el-icon-info help"></i>
                            </span>
                        </el-tooltip>
                        <el-input :value="toMoney(afterAdjustment.payment_gap)" :disabled="true"
                            style="width: 130px"></el-input>
                    </el-form-item>
                    <el-form-item label="次级IRR(%)">
                        <el-tooltip placement="bottom">
                            <div slot="content">
                                <div>运用XIRR函数迭代求解使(次级本金+利息)现值总和为零的年化折现率，即次级投资人实际收益率
                                </div>
                            </div>
                            <span class="mr-5">
                                <i class="el-icon-info help"></i>
                            </span>
                        </el-tooltip>
                        <el-input v-model="afterAdjustment.secondary_irr" :disabled="true"
                            style="width: 130px"></el-input>
                    </el-form-item>
                    <el-form-item label="可变报酬占比(含超额)(%)">
                        <el-tooltip placement="bottom">
                            <div slot="content">
                                <div>
                                    SPV内原始权益人收益 = “浮动服务费 ” + 自持部分利息分配 + 清仓回购“超额报酬”；<br />
                                    SPV总收益 = 资产包总现金流入 - 券端本金分配 - 循环购买支出；<br />
                                    VR（可变报酬） = SPV内原始权益人收益现值/SPV总收益现值。<br />
                                </div>
                            </div>
                            <span class="mr-5">
                                <i class="el-icon-info help"></i>
                            </span>
                        </el-tooltip>
                        <el-input v-model="afterAdjustment.var_comp_ratio_incl_excess" :disabled="true"
                            style="width: 130px"></el-input>
                    </el-form-item>
                    <el-form-item label="超额报酬占比(%)">
                        <el-tooltip placement="bottom">
                            <div slot="content">
                                <div>
                                    超额占比(%) = 超额报酬实付/基础回购价格
                                </div>
                            </div>
                            <span class="mr-5">
                                <i class="el-icon-info help"></i>
                            </span>
                        </el-tooltip>
                        <el-input v-model="afterAdjustment.full_excess_service_fee_ratio" :disabled="true"
                            style="width: 130px"></el-input>
                    </el-form-item>
                </el-form>
            </div>
        </base-box>
        <base-box title="调整详情">
            <template v-slot:header>
                <el-button type="primary" @click="applyPlan">应用调整后折价率</el-button>
            </template>
            <el-table :data="tableData">
                <el-table-column label="资产分类" prop="asset_category"></el-table-column>
                <el-table-column label="回购起算日资产余额" prop="balance" :formatter="formatMoney"></el-table-column>
                <el-table-column label="调整前回购价格" prop="price" :formatter="formatMoney"
                    label-class-name="before-adjust-bg"></el-table-column>
                <el-table-column label="调整前回购折价率(%)" prop="rate" label-class-name="before-adjust-bg"
                    :formatter="formatPercent"></el-table-column>
                <el-table-column label="调整后回购价格" prop="adjustment_price" :formatter="formatMoney"
                    label-class-name="after-adjust-bg"></el-table-column>
                <el-table-column label="调整后回购折价率(%)" prop="adjustment_rate" label-class-name="after-adjust-bg"
                    :formatter="formatPercent"></el-table-column>
            </el-table>
        </base-box>
    </div>
</template>


<script>
import utils from '@/utils/';
import BaseBox from '@/components/base/BaseBox.vue';
import {
    getBusinessRule,
    getRepurchaseAdjustmentDates,
    getRepurchaseMetrics,
    getScenarioList,
    getRepurchaseAdjustmentRate,
    updateRepurchaseAdjustmentRate
} from '@/api/Api';
import TaskProcess from "@/api/TaskProcess";

const initAdjustment = () => {
    return {
        "repurchase_calculation_date": "", // 回购起算日
        "payment_gap": "", // 兑付缺口
        "repurchase_price": "", // 清仓回购价格
        "secondary_irr": "", // 次级irr(%)
        "secondary_allocation": "", // 最后一期次级实付
        "var_comp_ratio_incl_excess": "", // 可变报酬占比(含超额)(%)
        "asset_service_fee": "", // 浮动服务费实付
        "full_excess_service_fee": "", // 超额报酬实付
        "full_excess_service_fee_ratio": "", // 超额报酬占比(%)
        "step_id": "" // 步骤ID
    }
};

export default {
    components: {
        BaseBox
    },
    data() {
        return {
            trustOptions: [],
            form: {
                trustId: '',
                calcDate: '',
                currentDate: '',
                scenarioId: '',
                basis: '', // 计算基准
                irr_up: '', // IRR上限
                irr_down: '', // IRR下限
                vc_proportion_up: '', // 可变报酬占比上限
                vc_proportion_down: '', // 可变报酬占比下限
                service_rate_up: '', // 服务费率上限
                service_rate_down: '' // 服务费率下限
            },
            today: (new Date()).Format('yyyy-MM-dd'),
            loading: false,
            dateOptions: [],
            scenarioOptions: [],
            tableData: [],
            beforeAdjustment: initAdjustment(),
            afterAdjustment: initAdjustment()
        }
    },
    computed: {
        scenario() {
            return this.scenarioOptions.find(v => v.task_session_id == this.form.scenarioId);
        },
        pickerOptions() {
            const _this = this;
            return {
                disabledDate(time) {
                    const dates = [..._this.dateOptions, _this.today];
                    return !dates.includes(time.Format('yyyy-MM-dd'));
                },
            }
        },
        dataDate() {
            if (this.form.calcDate) {
                const date = new Date(this.form.calcDate);
                date.setDate(date.getDate() - 1);
                return date.Format('yyyy-MM-dd');
            }
            return '';
        }
    },
    mounted() {
        this.form.calcDate = this.today;
        this.getTrustOptions();
    },
    methods: {
        formatPercent(row, column, value) {
            if (value == null) return '';
            return Number(value).toFixed(4);
        },
        formatMoney(row, column, value) {
            return this.toMoney(value);
        },
        toMoney(value) {
            // null 要显示出来，不能为0
            if (value == null || value === '') return '';
            return utils.formatCurrency(Number(value).toFixed(2));
        },
        getTrustOptions() {
            let svc = this.$http('FixedIncome', 'TrustManagement.usp_GetTrustIdAndName');
            svc.ExecTable().then(res => {
                if (res) {
                    this.trustOptions = res.filter(v => v.IsMarketProduct);
                }
            });
        },
        getDateOptions(val) {
            this.form.calcDate = this.today;
            this.scenarioOptions = [];
            this.tableData = [];
            this.beforeAdjustment = initAdjustment();
            this.afterAdjustment = initAdjustment();

            this.getScenarioOptions();
            getRepurchaseAdjustmentDates(val).then(res => {
                this.dateOptions = res.result;
            });

            getBusinessRule(val).then(res => {
                for (let key in res.result) {
                    this.form[key] = res.result[key];
                }
            })
        },
        getScenarioOptions() {
            this.form.scenarioId = '';
            getScenarioList(this.form.trustId, this.form.calcDate).then(res => {
                if (res) {
                    this.scenarioOptions = res.result;

                    const baseScenario = this.scenarioOptions.find(v => v.baseline_scenario);

                    if (baseScenario) {
                        this.form.scenarioId = baseScenario.task_session_id;
                    } else {
                        if (this.scenarioOptions.length === 0) return;
                        const lastItem = this.scenarioOptions.reduce((latest, current) => {
                            // 比较时间戳，取较大的那个
                            return current.updated_at > latest.updated_at ? current : latest;
                        }, this.scenarioOptions[0]);
                        this.form.scenarioId = lastItem.task_session_id;
                    }
                    this.getBeforeAdjustment();
                }
            });
        },
        getBeforeAdjustment() {
            if (this.form.scenarioId === '') return;
            getRepurchaseMetrics(this.form.scenarioId, this.form.calcDate).then(res => {
                if (res.result && res.result.length) {
                    this.beforeAdjustment = res.result.find(v => v.step_id === 1);
                    if (this.beforeAdjustment) {
                        this.beforeAdjustment.repurchase_calculation_date = new Date(this.beforeAdjustment.repurchase_calculation_date
                    ).Format('yyyy-MM-dd');
                    } else {
                        this.beforeAdjustment = initAdjustment();
                    }
                    this.afterAdjustment = res.result.find(v => v.is_final === true);
                    if (!this.afterAdjustment) {
                        this.afterAdjustment = initAdjustment();
                    }
                }
            });
            this.getRepurchaseAdjustmentRate();
        },
        getRepurchaseAdjustmentRate() {
            getRepurchaseAdjustmentRate(this.form.scenarioId, this.form.calcDate).then(res => {
                this.tableData = res.result;
            })
        },
        runTask() {
            if (this.form.irr_down === '' || this.form.irr_down === null) {
                return this.$message.error('次级IRR下限未配置');
            }
            if (this.form.vc_proportion_up === '' || this.form.vc_proportion_down === '' || this.form.vc_proportion_up === null || this.form.vc_proportion_down === null) {
                return this.$message.error('可变报酬占比(含超额)上限或下限未配置完整');
            }
            if (this.form.service_rate_up === '' || this.form.service_rate_down === '' || this.form.service_rate_up === null || this.form.service_rate_down === null) {
                return this.$message.error('超额占比上限或下限未配置完整');
            }
            let tp = new TaskProcess('Task', 'Repurchase_Adjustment');
            tp.AddVariableItem("trust_id", this.form.trustId, "String");
            tp.AddVariableItem("task_session_id", this.scenario.task_session_id, "String");
            tp.AddVariableItem("category", this.scenario.category, "String");
            tp.AddVariableItem("simulation_start_date", this.form.calcDate, "String");
            tp.ShowIndicator(() => {
                this.getBeforeAdjustment();
            });
        },
        applyPlan() {
            if (this.tableData.length === 0) return this.$message.error('缺少数据');
            const data = {
                TrustId: this.form.trustId
            };
            const isNull = this.tableData.some(v => v.adjustment_rate === null);
            if (isNull) return this.$message.error('未找到调整后的折价率，无法应用');
            this.tableData.forEach(v => {
                data[v.asset_category.replace('M6+', 'M6plus')] = this.formatPercent(null, null, v.adjustment_rate);
            })
            updateRepurchaseAdjustmentRate(data).then(res => {
                this.$message.success('提交成功');
            });
        }
    }
}
</script>

<style lang="scss" scoped>
.repo-discount-opt {
    height: 100%;
    overflow-y: auto;

    .yq-table{
        tr{
            border-bottom: 1px solid #efefef;
        }

        tr:first-child{
            border-top: 1px solid #ddd;
        }
        tr:last-child{
            border-bottom: 1px solid #ddd;
        }

        th,td{
            padding: 3px 10px;
        }

        th{
            text-align: left;
            font-weight: normal;
        }
        td{
            width: 100px;
        }
    }

    /deep/ .before-adjust-bg{
        background-color: #ffeee5;
    }
    /deep/ .after-adjust-bg{
        background-color: #e5efff;
    }
}
</style>

<style lang="scss">
.max-height-select-dropdown {
    .el-scrollbar__wrap {
        max-height: 400px !important;
    }
}
</style>