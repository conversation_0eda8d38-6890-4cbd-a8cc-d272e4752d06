<template>
    <div>
        <div class="top-meta">
            <div class="top-meta-label">
                回购起算日
            </div>
            <div>
                {{ repurchase_date }}
            </div>
        </div>
        <div class="top-meta">
            <div class="top-meta-label">
                回购基准
            </div>
            <div>
                {{ repurchase_price_basis }}
            </div>
        </div>
        <el-table :data="data">
            <el-table-column label="资产分类" prop="asset_category" width="120px"></el-table-column>
            <el-table-column label="回购起算日资产余额(元)" prop="balance" :formatter="formatMoney"></el-table-column>
            <el-table-column label="回购价格(元)" prop="price">
                <template v-slot="{row}">
                    <div v-if="row.repurchase_price_basis === '本息余额'">
                        {{ formatMoney(null, null, row.price_pi) }}
                    </div>
                    <div v-else>
                        {{ formatMoney(null, null, row.price) }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="回购折价率(%)" prop="discount_rate" width="125px" :formatter="formatPercent">
                <template v-slot="{ row }">
                    <div v-if="row.repurchase_price_basis === '本息余额'">
                        {{ formatPercent(null, null, row.discount_rate_pi) }}
                    </div>
                    <div v-else>
                        {{ formatPercent(null, null, row.discount_rate) }}
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
import { getScenarioRepurchaseInfo } from '@/api/Api';
import utils from '@/utils/index';
export default {
    props: {
        trustId: {
            type: [String, Number],
            default: '',
        },
        sessionId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            data: [],
            repurchase_date: '',
            repurchase_price_basis: '',
        }
    },
    watch: {
        sessionId() {
            this.getScenarioRepurchaseInfo();
        },
    },
    mounted() {
        this.getScenarioRepurchaseInfo();
    },
    methods: {
        formatPercent(row, column, value) {
            if (value == null) return '';
            return Number(value).toFixed(4);
        },
        formatMoney(row, column, value) {
            return this.toMoney(value);
        },
        toMoney(value) {
            // null 要显示出来，不能为0
            if (value == null || value === '') return '';
            return utils.formatCurrency(value);
        },
        getScenarioRepurchaseInfo() {
            if (!this.sessionId) return;
            this.repurchase_date = '';
            getScenarioRepurchaseInfo(this.trustId, this.sessionId).then(res => {
                this.data = res.result;
                if (res.result.length) {
                    if (res.result[0].repurchase_date) {
                        this.repurchase_date = new Date(res.result[0].repurchase_date).Format("yyyy-MM-dd");
                    }
                    if (res.result[0].repurchase_price_basis) {
                        this.repurchase_price_basis = res.result[0].repurchase_price_basis;
                    }
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.top-meta{
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .top-meta-label{
        width: 120px;
        color: #888;
    }
}
</style>