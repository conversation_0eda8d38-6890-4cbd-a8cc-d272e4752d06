<template>
  <div class="repurchaseConfigView">
    <el-scrollbar style="height:100%;">
      <div class="card-box">
        <div class="toolbar">
          <div class="tool-title" style="max-width: 150px;">
            <span class="titleSpan">
              {{ $t('productDetail.productConfig.callBack.RepurchaseSettings') }}
            </span>
          </div>
          <div class="tool-buttons">
            <label class="mr-5">是否计算回购折价率: </label>
            <el-radio v-model="priceSetting.IsCalculated" :label="true">是</el-radio>
            <el-radio v-model="priceSetting.IsCalculated" :label="false">否</el-radio>
            <el-button @click="openDiscountRef">回购折价率参考</el-button>
            <el-button type="primary" @click="saveExtSetting" :disabled="isCaution == 1">{{ $t('common.Save')
              }}</el-button>
          </div>
        </div>
        <el-form ref="form" :inline="true" label-position="left">
          <el-form-item :label="$t('productDetail.productConfig.callBack.PriceBase')"
            :label-width="locale === 'zh' ? '70px' : '90px'">
            <el-select v-model="priceSetting.CalculateBasis" style="width:180px;">
              <el-option :label="'1-' + $t('productDetail.productConfig.callBack.PoolBalanceOpening')"
                value="PoolBalance_Opening"></el-option>
              <el-option :label="'2-' + $t('productDetail.productConfig.callBack.PoolBalance')"
                value="PoolBalance"></el-option>
              <el-option :label="'3-' + $t('productDetail.productConfig.callBack.PoolBalancePIOpening')"
                value="PoolBalancePI_Opening"></el-option>
              <el-option :label="'4-' + $t('productDetail.productConfig.callBack.PoolBalancePI')"
                value="PoolBalancePI"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label-width="135px">
            <template v-slot:label>
              <el-tooltip>
                <div slot="content">
                  <p>这里填的折价率仅用于算本金部分回购价款，利息部分回购价款由情景预测现金流决定。</p>
                  <p>本金折价：回购价款 = 本金余额 * 回购折价率</p>
                  <p>本息折价：回购价款 = 本金余额 * 本金回购折价率 + 预测利息金额</p>
                </div>
                <span>
                  {{ $t('productDetail.productConfig.callBack.Discount') }}
                  <i class="el-icon-info help"></i>
                </span>
              </el-tooltip>
            </template>

            <el-select v-model="priceSetting.DiscountRate" style="width:190px;">
              <el-option :label="$t('productDetail.productConfig.callBack.CumulativeDefaultRate')"
                value="CumulativeDefaultRate" :disabled="priceSetting.CalculateBasis === 'BondBalance'"></el-option>
              <el-option :label="$t('productDetail.productConfig.callBack.SpecifiedCallDiscount')"
                value="SpecifiedCallDiscount"></el-option>
              <el-option :label="$t('productDetail.productConfig.callBack.OverdueArea')" value="OverdueArea"
                :disabled="priceSetting.CalculateBasis === 'BondBalance'"></el-option>
            </el-select>
            <div v-show="isOverdueArea">
              <div class="discount-rate-panel">
                <div class="trigger-container">
                  <span> M0折价率(%)： </span> <el-input v-model="priceSetting.M0"></el-input>
                </div>
                <div class="trigger-container">
                  <span> M1折价率(%)： </span> <el-input v-model="priceSetting.M1"></el-input>
                </div>
                <div class="trigger-container">
                  <span> M2折价率(%)： </span> <el-input v-model="priceSetting.M2"></el-input>
                </div>
                <div class="trigger-container">
                  <span> M3折价率(%)： </span> <el-input v-model="priceSetting.M3"></el-input>
                </div>
                <div class="trigger-container">
                  <span> M4折价率(%)： </span> <el-input v-model="priceSetting.M4"></el-input>
                </div>
                <div class="trigger-container">
                  <span> M5折价率(%)： </span> <el-input v-model="priceSetting.M5"></el-input>
                </div>
                <div class="trigger-container">
                  <span> M6折价率(%)： </span> <el-input v-model="priceSetting.M6"></el-input>
                </div>
                <div class="trigger-container">
                  <span> M6+折价率(%)： </span> <el-input v-model="priceSetting.M6plus"></el-input>
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item v-show="isSpecifiedCallDiscount"
            :label="$t('productDetail.productConfig.callBack.FundsInclude')" label-width="130px">
            <el-input-number style="width:100px;" v-model="priceSetting.SpecifiedCallDiscount"
              :controls="false"></el-input-number>
          </el-form-item>
          <el-form-item :label="$t('productDetail.productConfig.callBack.CallAvailableAmount')"
            :label-width="locale === 'zh' ? '100px' : '190px'">
            <el-select v-model="priceSetting.CallAvailableAmount" style="width:194px;">
              <el-option :label="$t('productDetail.productConfig.callBack.CallPriceWithPoolCollection')"
                value="CallPriceWithPoolCollection"></el-option>
              <el-option :label="$t('productDetail.productConfig.callBack.CallPriceOnly')"
                value="CallPriceOnly"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item :label="$t('productDetail.productConfig.callBack.CallPriceActual')" label-width="80px">
            <el-input-number style="width:100px;" v-model="priceSetting.CallPriceActual"
              :controls="false"></el-input-number>
          </el-form-item>

          <!-- <el-form-item label="触发设置">
                <el-radio-group v-model="priceSetting.TriggerNextMonth">
                  <el-radio :label="0">{{ $t('productDetail.productConfig.callBack.NoTrigger') }}</el-radio>
                  <el-radio :label="1">{{ $t('productDetail.productConfig.callBack.TriggeredInTheCurrentMonth') }}</el-radio>
                  <el-radio :label="2">{{ $t('productDetail.productConfig.callBack.TriggeredNextMonth') }}</el-radio>
                </el-radio-group>
              </el-form-item> -->
        </el-form>
      </div>
      <div class="card-box">
        <div class="toolbar">
          <div class="tool-title">
            <span class="titleSpan">{{ $t('productDetail.productConfig.callBack.TriggerSettings') }}</span>
          </div>
          <div class="tool-buttons">
            <el-button @click="addScenario" :title="$t('productDetail.productConfig.revolvingPlan.AddTrigger')"
              v-show="scenarios.length == 0">
              <i class="el-icon-plus"></i>
              {{ $t('productDetail.productConfig.callBack.AddTrigger') }}
            </el-button>
          </div>
        </div>
        <div class="scenarios" v-show="scenarios.length && showSettingPanel">
          <div class="scenarios-panel" v-for="(scenario, index) in scenarios" :key="index">
            <div class="scenarios-head">
              <div class="fr">
                <el-button type="primary" @click="saveScenario(index)"><i class="iconfont icon-baocun2"></i>{{
                  $t('common.Save') }}</el-button>
                <el-button @click="removeScenario(index)" type="danger">
                  <i class="el-icon-delete"></i> {{ $t('common.Delete') }}
                </el-button>
              </div>
              <el-input v-model="scenario.scenarioName" class="scenarios-name"></el-input>
            </div>
            <Container :container="containers[index]" :options="conditionOptions"></Container>
          </div>
        </div>
        <div v-if="scenarios.length === 0" class="el-table__empty-block">
          <span class="el-table__empty-text">{{ $t('common.NoData') }}</span>
        </div>
        <div class="card-unfold" v-show="scenarios.length" @click="showSettingPanel = !showSettingPanel">
          <i class="icon" :class="[showSettingPanel ? 'icon-up-open-big' : 'icon-down-open-big']"></i>
        </div>
      </div>
    </el-scrollbar>

    <el-dialog title="回购折价率参考" :visible.sync="showDiscountRef" width="95vw" top="5vh">
      <AllScenarioMobPriceSummaryTable @selected="handleSelected"></AllScenarioMobPriceSummaryTable>
    </el-dialog>
  </div>
</template>

<script>
import Container from "./Container";
import { ServiceUrlMysql } from "@/utils/config";
import axios from "axios";
import AllScenarioMobPriceSummaryTable from "./AllScenarioMobPriceSummaryTable.vue";
import AllScenarioMobPriceSummaryPITable from "./AllScenarioMobPriceSummaryPITable.vue";
import utils from '@/utils/';

export default {
  name: "RepurchaseConfigView",
  props: {
    TrustId: {
      type: Number,
      required: true
    },
    isCaution: [String, Number]
  },
  components: { Container, AllScenarioMobPriceSummaryTable, AllScenarioMobPriceSummaryPITable },
  data() {
    return {
      showSettingPanel: true,
      priceSetting: {
        SpecifiedCallDiscount: 0,
        OverdueArea: "",
        CalculateBasis: "",
        DiscountRate: "",
        CallAvailableAmount: "",
        CallPriceActual: "",
        M0: 0,
        M1: 0,
        M2: 0,
        M3: 0,
        M4: 0,
        M5: 0,
        M6: 0,
        M6plus: 0,
        TriggerNextMonth: 0,
        IsCalculated: "0",
      },
      scenarios: [],
      containers: [],
      conditionOptions: [],
      showDiscountRef: false,
    };
  },
  created() {
    this.getBondRepurchaseData();
    this.getExtSetting();
  },
  computed: {
    isSpecifiedCallDiscount() {
      return (this.priceSetting.DiscountRate === 'SpecifiedCallDiscount')
    },
    isOverdueArea() {
      return (this.priceSetting.DiscountRate === 'OverdueArea')
    },
    locale() {
      return this.$store.state.locale;
    }
  },
  methods: {
    openDiscountRef() {
      this.showDiscountRef = true;
    },
    getBondRepurchaseData: function () {
      let self = this;
      this.scenarios = [];
      this.containers = [];
      this.conditionOptions = [];

      let url = ServiceUrlMysql + 'solutionRevolvingPlan/getScenarios';
      axios.get(url, {
        params: { 'trustId': self.TrustId }
      }).then(res => {
        self.conditionOptions = res.data.result[0];
        if (res.data.result[1].length) {
          res.data.result[1].forEach(function (v, i) {
            if (i > 0) {
              return
            }
            self.scenarios.push({
              ID: v.ID,
              scenarioName: v.ScenarioName,
              scenarioDescription: v.ScenarioDescription
            });
            if (res.data.result[2].length) {
              var target = {};
              target.containers = [];
              var checkContainer = function (target, source) {
                if (target.containerId === source.pContainerId) {
                  target.containers.push(source);
                } else if (target.containers.length !== 0) {
                  target.containers.forEach(function (childContainer) {
                    checkContainer(childContainer, source);
                  });
                }
              };

              res.data.result[2]
                .filter(function (b) {
                  return b.ScenarioID === v.ID;
                })
                .forEach(function (v) {
                  var obj = {
                    containerType: v.ContainerType,
                    containerId: v.ContainerID,
                    pContainerId: v.PContainerID,
                    containerDesc: v.ContainerDesc,
                    conditions: [],
                    containers: []
                  };
                  if (v.PContainerID === null) {
                    target = obj;
                  } else {
                    checkContainer(target, obj);
                  }
                });

              var checkCondition = function (target, source) {
                if (target.containerId === source.containerId) {
                  target.conditions.push(source);
                } else if (target.containers.length !== 0) {
                  target.containers.forEach(function (childContainer) {
                    checkCondition(childContainer, source);
                  });
                }
              };

              if (res.data.result[3].length) {
                res.data.result[3]
                  .filter(function (b) {
                    return b.ScenarioID === v.ID;
                  })
                  .forEach(function (v) {
                    var obj = {
                      containerId: v.ContainerID,
                      conditionItem: v.ConditionItem,
                      conditionId: v.ConditionID,
                      operator: v.Operator,
                      value: v.Value
                    };
                    checkCondition(target, obj);
                  });
              }

              self.containers.push(target);
            }
          });
        }
      });
    },
    getExtSetting: async function () {
      const self = this;
      let url = ServiceUrlMysql + 'solutionRevolvingPlan/getExtSetting';
      self.priceSetting = await axios.get(url, {
        params: { 'trustId': self.TrustId },
        headers: {
          'page-url': window.location.href
        }
      }).then(res => {
        if (res.data.result) {
          return Promise.resolve(res.data.result[0] ? res.data.result[0] : self.priceSetting);
        }
      });
    },
    saveExtSetting() {
      const self = this;
      let { SpecifiedCallDiscount, CalculateBasis, DiscountRate, CallAvailableAmount, CallPriceActual, M0, M1, M2, M3, M4, M5, M6, M6plus, TriggerNextMonth, IsCalculated } = this.priceSetting;

      if (!this.isSpecifiedCallDiscount) {
        SpecifiedCallDiscount = 0;
      }
      if (this.priceSetting.DiscountRate === 'OverdueArea') {
        const isOk = M0 !== '' && M1 !== '' && M2 !== '' && M3 !== '' && M4 !== '' && M5 !== '' && M6 !== '';
        if (!isOk) {
          self.$message.error('请填写所有区间折价率');
          return
        }
      }
      let callApi = this.$http('FixedIncomeSuite', 'Analysis.BondRepurchase_SaveExtSetting', true);
      callApi.AddParam({ Name: 'TrustID', Value: this.TrustId, DBType: 'int' });
      callApi.AddParam({ Name: 'CalculateBasis', Value: CalculateBasis, DBType: 'string' });
      callApi.AddParam({ Name: 'DiscountRate', Value: DiscountRate, DBType: 'string' });
      callApi.AddParam({ Name: 'SpecifiedCallDiscount', Value: SpecifiedCallDiscount.toString(), DBType: 'string' });
      callApi.AddParam({ Name: 'CallAvailableAmount', Value: CallAvailableAmount, DBType: 'string' });
      callApi.AddParam({ Name: 'CallPriceActual', Value: CallPriceActual, DBType: 'string' });
      callApi.AddParam({ Name: 'TriggerNextMonth', Value: TriggerNextMonth, DBType: 'int' });
      callApi.AddParam({ Name: 'IsCalculated', Value: Number(IsCalculated), DBType: 'int' });
      callApi.AddParam({ Name: 'M0', Value: M0, DBType: 'string' });
      callApi.AddParam({ Name: 'M1', Value: M1, DBType: 'string' });
      callApi.AddParam({ Name: 'M2', Value: M2, DBType: 'string' });
      callApi.AddParam({ Name: 'M3', Value: M3, DBType: 'string' });
      callApi.AddParam({ Name: 'M4', Value: M4, DBType: 'string' });
      callApi.AddParam({ Name: 'M5', Value: M5, DBType: 'string' });
      callApi.AddParam({ Name: 'M6', Value: M6, DBType: 'string' });
      callApi.AddParam({ Name: 'M6plus', Value: M6plus, DBType: 'string' });
      callApi.ExecTable().then((res) => {
        self.$message({
          type: 'success',
          message: self.$t('common.SaveSuccessful')
        })
      });
    },
    addScenario() {
      if (this.scenarios.length === 0) {
        this.scenarios.push({
          scenarioName: "回购情景" + (this.scenarios.length + 1),
          scenarioDescription: ""
        });
        let container = {
          containerType: "and",
          containerDesc: "",
          conditions: [],
          containers: []
        };
        let options = this.conditionOptions;
        let newCondition = {
          conditionId: 1,
          conditionItem: options.length ? options[0].ItemCode : "",
          operator: ">",
          value: ''
        };
        container.conditions.push(newCondition);
        this.containers.push(container);
      } else {
        this.$message('只能新增或编辑一个情景!');
      }
    },
    saveScenario(index) {
      const self = this;
      // 保存单个情景
      var scenario = this.scenarios[index];
      var container = this.containers[index];
      // 生成XML结构
      var createItemsXml = function (data) {
        var items = '', item;
        data.forEach(function (v) {
          item = '';
          for (var k in v) {
            item += '<' + k + '>' + (v[k] !== null ? v[k] : "") + '</' + k + '>';
          }
          items += '<Item>' + item + '</Item>';
        });
        return '<Items>' + items + '</Items>';
      };

      var containerResut = this.resolveContainers(container);
      var conditionResut = this.resolveConditions(container);

      if (conditionResut.length === 0) {
        self.$message.error('请添加触发条件！');
        return;
      }

      var containerData = createItemsXml(containerResut);
      var conditionData = createItemsXml(conditionResut);

      var callApi = this.$http('FixedIncomeSuite', 'Analysis.BondRepurchase_SaveTriggerScenario', true);
      callApi.AddParam({ Name: 'ID', Value: scenario.ID || 0, DBType: 'int' });
      callApi.AddParam({ Name: 'TrustID', Value: parseInt(this.TrustId), DBType: 'int' });
      callApi.AddParam({ Name: 'ScenarioName', Value: scenario.scenarioName, DBType: 'string' });
      callApi.AddParam({ Name: 'ScenarioDescription', Value: scenario.scenarioDescription, DBType: 'string' });
      callApi.AddParam({ Name: 'ContainerData', Value: containerData, DBType: 'xml' });
      callApi.AddParam({ Name: 'ConditionData', Value: conditionData, DBType: 'xml' });
      callApi.ExecTable().then((res) => {
        self.getBondRepurchaseData();
        self.$message({
          type: 'success',
          message: '保存成功！'
        })
      })
    },
    resolveContainers(container) {
      var mapResult = [], containerId = 0, pContainerId = null;
      var mapContainer = function (container, parentContainer) {
        if (container.containerId) {
          containerId = container.containerId;
        } else {
          containerId += 1;
        }
        if (parentContainer) {
          pContainerId = parentContainer.ContainerID;
        }
        var currentContainer = {
          ContainerID: containerId,
          PContainerID: pContainerId,
          ContainerType: container.containerType,
          ContainerDesc: container.containerDesc,
        };
        mapResult.push(currentContainer);
        if (container.containers.length) {
          container.containers.forEach(function (childContainer) {
            mapContainer(childContainer, currentContainer);
          });
        }
      };
      mapContainer(container);
      return mapResult;
    },
    resolveConditions(container) {
      console.log(container)
      var mapResult = [], containerId = 0, conditionId = 0;
      var mapCondition = function (container) {
        if (container.containerId) {
          containerId = container.containerId;
        } else {
          containerId += 1;
        }
        if (container.conditions.length) {
          container.conditions.forEach(function (v) {
            if (v.conditionId) {
              conditionId = v.conditionId;
            } else {
              conditionId += 1;
            }
            mapResult.push({
              ContainerID: containerId,
              ConditionID: conditionId,
              ConditionItem: v.conditionItem,
              Operator: '<![CDATA[' + v.operator + ']]>',
              Value: v.value
            });
          });
        }
        if (container.containers.length) {
          container.containers.forEach(function (childContainer) {
            mapCondition(childContainer);
          });
        }
      }
      mapCondition(container);
      return mapResult;
    },
    removeScenario(index) {
      var scenario = this.scenarios[index];
      const self = this;
      this.$confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (scenario.ID) {
          var callApi = this.$http('FixedIncomeSuite', 'Analysis.BondRepurchase_RemoveTriggerScenario');
          callApi.AddParam({ Name: 'ScenarioID', Value: scenario.ID, DBType: 'int' });
          callApi.ExecTable().then((res) => {
            self.getBondRepurchaseData();
            self.$message('删除成功!');
          });
        } else {
          self.scenarios.splice(index, 1);
          self.containers.splice(index, 1);
          self.$message('删除成功!');
        }
      })
    },
    handleSelected(item) {
      const capitalize = function (str) {
        if (!str) return '';
        return str[0].toUpperCase() + str.slice(1);
      };
      for (let key in item) {
        if (key.includes('pricing_')) {
          let val = item[key];
          val = val !== null && val !== undefined ? utils.floatMul(val, 100) : '';
          this.priceSetting[capitalize(key.replace('pricing_', ''))] = val;
        }
      }
      this.showDiscountRef = false;
      this.$message.info('折价率逾期区间已更新！')
    } 
  }
};
</script>

<style lang="scss" scoped>
.repurchaseConfigView {
  .card-box {
    padding: 15px;
    padding-bottom: 0;

    .toolbar {
      display: flex;
      height: 40px;

      .tool-title {
        flex: 1;
      }

      .tool-buttons {
        flex: 1;
        text-align: right;
      }
    }

    .scenarios-head {
      margin: 15px 0;
    }

    .scenarios-name {
      width: 200px;

    }

    /deep/ .el-input__inner {
      height: 30px;
      line-height: 30px;
    }

    /deep/ .el-input-number--mini {
      width: auto;
    }

    /deep/ .el-form-item--mini.el-form-item {
      margin: 10px 0;
    }
  }

  .titleSpan {
    position: relative;
    padding-left: 20px;
    font-size: 14px;
    line-height: 30px;

    &:before {
      position: absolute;
      top: 50%;
      left: 0;
      margin-top: -4px;
      display: inline-block;
      content: '';
      width: 8px;
      height: 8px;
      background: $--color-primary;
    }
  }
}
.discount-rate-panel{
  margin-top: 15px;
  position: relative;
  border-radius: 8px;
  padding: 10px;
  border: 1px solid #d5d9e6;
  &::before {
      content: "";
      position: absolute;
      top: -5px;
      left: 20px;
      width: 10px;
      height: 10px;
      background-color: #fff;
      border-left: 1px solid #d5d9e6;
      border-top: 1px solid #d5d9e6;
      transform: rotate(45deg);
      border-radius: 3px;
      box-shadow: -2px -2px 2px rgba(0, 0, 0, 0.05);
  }
  .trigger-container{
    margin-top: 6px;
    margin-bottom: 6px;

    .el-input{
      width: 76px;
    }

    > span{
      width: 119px;
      display: inline-block;
    }
  }
}
</style>
