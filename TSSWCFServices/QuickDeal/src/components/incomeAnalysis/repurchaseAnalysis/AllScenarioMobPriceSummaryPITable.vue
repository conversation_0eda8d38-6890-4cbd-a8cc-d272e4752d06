<template>
    <div>
        <el-table ref="table" v-loading="loading" :data="tableData" height="70vh">
            <el-table-column prop="session_id" label="测算情景" width="108px" fixed="left"></el-table-column>
            <el-table-column prop="scenario_id" label="情景ID" min-width="90px" fixed="left"></el-table-column>
            <!-- <el-table-column prop="baseline_scenario" label="是否基准情景" min-width="120px" fixed="left">
                <template v-slot="{ row }">
                    <div>{{ row.baseline_scenario === null ? '-' : row.baseline_scenario == 1 ? '是' : '否' }}</div>
                </template>
            </el-table-column> -->
            <el-table-column prop="scenario_desc" label="情景描述" width="240px" fixed="left"></el-table-column>
            <el-table-column prop="simulation_start_date" label="测算日期" width="110px" :formatter="formatDate"
                fixed="left"></el-table-column>
            <el-table-column prop="repurchase_calculation_date" label="回购起算日" width="110px" :formatter="formatDate"
                fixed="left"></el-table-column>
            <el-table-column prop="" label="回购折价率(本息)(%)" header-align="center">
                <el-table-column v-for="item in tableHead" :prop="'pricing_' + item.toLowerCase()" :label="item"
                    width="90px" :formatter="formatPercent" header-align="center" align="right"></el-table-column>
            </el-table-column>
            <el-table-column prop="" label="回购起算日资产余额(本金)" header-align="center">
                <el-table-column v-for="item in tableHead" :prop="item.toLowerCase()" :label="item" width="140px"
                    :formatter="formatMoney" header-align="center" align="right"></el-table-column>
            </el-table-column>
            <el-table-column prop="" label="回购价格(本息)" header-align="center">
                <el-table-column v-for="item in tableHead" :prop="'price_' + item.toLowerCase()" :label="item"
                    width="140px" :formatter="formatMoney" header-align="center" align="right"></el-table-column>
            </el-table-column>
        </el-table>
        <div class="mt-10">
            <el-pagination background @current-change="initData" :page-size.sync="pager.pageSize"
                :current-page.sync="pager.pageNum" @size-change="initData" :total="pager.total" ref="page"
                layout="total, sizes, prev, pager, next, jumper" :page-sizes="[5, 10, 20, 50]"></el-pagination>
        </div>
    </div>
</template>
<script>
import { getAllScenarioMobPriceSummaryPI } from '@/api/Api';
import util from '@/utils/';
export default {
    data() {
        return {
            tableData: [],
            tableHead: ['M0', 'M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M6plus'],
            pager: {
                pageNum: 1,
                pageSize: 5
            },
            loading: false,
        }
    },
    mounted() {
        this.initData();
    },
    methods: {
        async initData() {
            this.loading = true;
            const res = await getAllScenarioMobPriceSummaryPI({
                trustId: this.$route.params.id,
                ...this.pager,
            });
            this.tableData = res.result.records;
            this.pager.total = res.result.total;
            this.loading = false;
            this.$nextTick(() => {
                this.$refs.table.doLayout();
            });
        },
        formatDate(row, col, val) {
            return new Date(val).Format('yyyy-MM-dd');
        },
        formatMoney(row, col, value) {
            if (value === undefined || value === null) return '';
            return util.formatCurrency(value);
        },
        formatPercent(row, col, value) {
            if (value === undefined || value === null) return '';
            return Number(util.floatMul(value, 100)).toFixed(4);
        }
    }
}
</script>