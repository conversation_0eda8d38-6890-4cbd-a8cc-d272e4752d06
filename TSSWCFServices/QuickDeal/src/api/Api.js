/*
 封装后台服务请求
 请求基于axios，文档https://github.com/axios/axios
*/
import axios from "axios";
import { ServiceUrlMysql } from "../utils/config.js";

// 创建 Axios 实例
const service = axios.create({
  baseURL: ServiceUrlMysql,
  headers: {
    "page-url": window.location.href,
  },
});

// 响应拦截器
service.interceptors.response.use(
  (res) => {
    let data = "";
    if (typeof res.data !== "undefined") {
      if (typeof res.data === "string") {
        try {
          data = JSON.parse(res.data);
        } catch (e) {
          data = res.data;
        }
      } else {
        data = res.data;
      }
    }
    return data;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 大象通知列表配置
export const messageConfigSaveUpdate = (data) => {
  const url = "MessageNotificationConfig/messageConfigSaveUpdate";
  return service.post(url, data);
};

// 大象消息通知配置列表查询接口
export const listMessageConfig = (data = {}) => {
  const url = "MessageNotificationConfig/listMessageConfig";
  return service.get(url, { params: data });
};

// 大象通知配置列表删除操作
export const deleteByIds = (data) => {
  const url = "MessageNotificationConfig/deleteByIds";
  return service.post(url, data);
};

// 大象通知列表通知人员配置列表查看
export const listMessageUserConfig = (data = {}) => {
  const url = "MessageNotificationConfig/listMessageUserConfig";
  return service.get(url, { params: data });
};

// 大象通知列表人员配置
export const configMessageNotificationUsers = (data) => {
  const url = "MessageNotificationConfig/configMessageNotificationUsers";
  return service.post(url, data);
};

// 人员配置列表添加或者更新
export const dxUserSaveUpdate = (data) => {
  const url = "dxUser/dxUserSaveUpdate";
  return service.post(url, data);
};

// 人员配置列表展示
export const findDXUsers = (data = {}) => {
  const url = "dxUser/findDXUsers";
  return service.get(url, { params: data });
};

// 人员配置列表删除操作
export const dxDeleteUserByIds = (data) => {
  const url = "dxUser/deleteByIds";
  return service.post(url, data);
};

export const taskInstancePage = (data) => {
  const url = "TaskInstance/page";
  return service.post(url, data);
};

export const taskInstanceReset = (data) => {
  const url = "TaskInstance/reset";
  return service.post(url, data);
};

export const getMetricsResult = (sessionId) => {
  const url = "ABSMetrics/getMetricsResult";
  return service.get(url, {
    params: { sessionId: sessionId },
  });
};

export const getArchiveDate = (trustId) => {
  const url = "AssetDeviation/getArchiveDate";
  return service.get(url, {
    params: { trustId },
  });
};

export const getArchiveRecords = (trustId, calcDate) => {
  const url = "AssetDeviation/getArchiveRecords";
  return service.get(url, {
    params: { trustId, calcDate },
  });
};

export const getAssetDeviation = (trustId, calcDate, sceneId) => {
  const url = "AssetDeviation/getAssetDeviation";
  return service.get(url, {
    params: { trustId, calcDate, sceneId },
  });
};

export const getAssetDeviationByMob = (trustId, calcDate, sceneId) => {
  const url = "AssetDeviation/getAssetDeviationByMob";
  return service.get(url, {
    params: { trustId, calcDate, sceneId },
  });
};

export const getTicketsEndDeviation = (trustId, sessionId, calcDate, currentDate) => {
  const url = "AssetDeviation/getTicketsEndDeviation";
  return service.get(url, {
    params: { trustId, sessionId, calcDate, currentDate },
  });
};

export const getLatestDataDate = (trustId) => {
  const url = "AssetDeviation/getLatestDataDate";
  return service.get(url, {
    params: { trustId },
  });
};

export const getTrustDistributionTrusts = (category = "weidai") => {
  const url = "TrustDistribution/trust";
  return service.get(url, {
    params: { category },
  });
};

export const getTrustDistributionTrustInfo = (trustId) => {
  const url = "TrustDistribution/trustInfo";
  return service.get(url, {
    params: { trustId },
  });
};

export const getTrustDistributionDistribution = (trustCode, reportingDate) => {
  const url = "TrustDistribution/distribution";
  return service.get(url, {
    params: { trustCode, reportingDate },
  });
};

// 融资仪表盘
export const getReportingDates = () => {
  const url = "FinDashboard/getReportingDates";
  return service.get(url);
};

export const getBondBalancePage = (data) => {
  const url = "FinDashboard/getBondBalancePage";
  return service.post(url, data);
};

export const getAssetRemainingPrincipal = (data) => {
  const url = "FinDashboard/getAssetRemainingPrincipal";
  return service.post(url, data);
};

export const getBalanceByReportingDate = (reportingDate) => {
  const url = "FinDashboard/getBalanceByReportingDate";
  return service.get(url, {
    params: { reportingDate },
  });
};

export const getSelfHoldingCpbByReportingDate = (reportingDate) => {
  const url = "FinDashboard/getSelfHoldingCpbByReportingDate";
  return service.get(url, {
    params: { reportingDate },
  });
};

export const getTrustCountByReportingDate = (reportingDate) => {
  const url = "FinDashboard/getTrustCountByReportingDate";
  return service.get(url, {
    params: { reportingDate },
  });
};

export const getTrustMetricPage = (data) => {
  const url = "FinDashboard/getTrustMetricPage";
  return service.post(url, data);
};

export const exportTrustMetric = (reportingDate) => {
  const url = ServiceUrlMysql + "FinDashboard/exportTrustMetric";
  return axios.get(url, {
    params: { reportingDate },
    responseType: "blob",
  });
};

// 风险与收益指标页面
export const getTrustInfo = () => {
  const url = "RiskAndReturnMetrics/getTrustInfo";
  return service.get(url);
};

export const getIRRMetricsResult = (trustId) => {
  const url = "RiskAndReturnMetrics/getIRRMetricsResult";
  return service.get(url, {
    params: { trustId },
  });
};

export const getAPRMetricsResult = (trustId) => {
  const url = "RiskAndReturnMetrics/getAPRMetricsResult";
  return service.get(url, {
    params: { trustId },
  });
};

export const getRIRMetricsResult = (trustId) => {
  const url = "RiskAndReturnMetrics/getRIRMetricsResult";
  return service.get(url, {
    params: { trustId },
  });
};

export const getThresholdConfig = (productCategory) => {
  const url = "FinDashboard/getThresholdConfig";
  return service.get(url, {
    params: { productCategory },
  });
};

export const saveThresholdConfig = (data) => {
  const url = "FinDashboard/saveThresholdConfig";
  return service.post(url, data);
};

export const getCostMetricData = (params) => {
  const url = "FinDashboard/getCostMetricData";
  return service.get(url, {
    params: params,
  });
};

export const getFinTrustInfo = (trustId) => {
  const url = "FinDashboard/getTrustInfo";
  return service.get(url, {
    params: { trustId },
  });
};

export const getAllScenarioMobPriceSummary = (params) => {
  const url = "solutionRevolvingPlan/getAllScenarioMobPriceSummary";
  return service.get(url, {
    params: params,
  });
};

export const getAllScenarioMobPriceSummaryPI = (params) => {
  const url = "solutionRevolvingPlan/getAllScenarioMobPriceSummaryPI";
  return service.get(url, {
    params: params,
  });
};

export const getBusinessRule = (trustId) => {
  const url = "solutionRevolvingPlan/getBusinessRule";
  return service.get(url, {
    params: {
      trustId,
    },
  });
};

export const getRepurchaseAdjustmentDates = (trustId) => {
  const url = "solutionRevolvingPlan/getRepurchaseAdjustmentDates";
  return service.get(url, {
    params: { trustId },
  });
};

export const getRepurchaseMetrics = (taskSessionId, simulationDate) => {
  const url = "solutionRevolvingPlan/getRepurchaseMetrics";
  return service.get(url, {
    params: { taskSessionId, simulationDate },
  });
};

export const getRepurchaseAdjustmentRate = (taskSessionId, simulationDate) => {
  const url = "solutionRevolvingPlan/getRepurchaseAdjustmentRate";
  return service.get(url, {
    params: { taskSessionId, simulationDate },
  });
};

export const getScenarioList = (trustId, simulationDate) => {
  const url = "solutionRevolvingPlan/getScenarioList";
  return service.get(url, {
    params: { trustId, simulationDate },
  });
};

export const updateRepurchaseAdjustmentRate = (data) => {
  const url = "solutionRevolvingPlan/updateRepurchaseAdjustmentRate";
  return service.post(url, data);
};

export const getScenarioRepurchaseInfo = (trustId, taskSessionId) => {
  const url = "solutionRevolvingPlan/getScenarioRepurchaseInfo";
  return service.get(url, {
    params: { trustId, taskSessionId },
  });
};

