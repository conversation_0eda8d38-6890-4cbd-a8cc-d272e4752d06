(function(t){function e(e){for(var n,i,s=e[0],c=e[1],l=e[2],h=0,f=[];h<s.length;h++)i=s[h],Object.prototype.hasOwnProperty.call(a,i)&&a[i]&&f.push(a[i][0]),a[i]=0;for(n in c)Object.prototype.hasOwnProperty.call(c,n)&&(t[n]=c[n]);u&&u(e);while(f.length)f.shift()();return o.push.apply(o,l||[]),r()}function r(){for(var t,e=0;e<o.length;e++){for(var r=o[e],n=!0,s=1;s<r.length;s++){var c=r[s];0!==a[c]&&(n=!1)}n&&(o.splice(e--,1),t=i(i.s=r[0]))}return t}var n={},a={4:0},o=[];function i(e){if(n[e])return n[e].exports;var r=n[e]={i:e,l:!1,exports:{}};return t[e].call(r.exports,r,r.exports,i),r.l=!0,r.exports}i.m=t,i.c=n,i.d=function(t,e,r){i.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},i.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},i.t=function(t,e){if(1&e&&(t=i(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(i.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var n in t)i.d(r,n,function(e){return t[e]}.bind(null,n));return r},i.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="";var s=window["webpackJsonp"]=window["webpackJsonp"]||[],c=s.push.bind(s);s.push=e,s=s.slice();for(var l=0;l<s.length;l++)e(s[l]);var u=c;o.push([11,1,0]),r()})({0:function(t,e){},"01ea":function(t,e,r){"use strict";r.d(e,"b",(function(){return a})),r.d(e,"a",(function(){return o})),r.d(e,"c",(function(){return i}));var n="../",a=n+"Services/CommonService.svc/",o=n+"CashFlowEngine/",i=n+"Services/"},"0e30":function(t,e,r){},1:function(t,e){},11:function(t,e,r){t.exports=r("7e47")},2:function(t,e){},"23a0":function(t,e,r){"use strict";r.d(e,"b",(function(){return i})),r.d(e,"c",(function(){return s})),r.d(e,"d",(function(){return c})),r.d(e,"e",(function(){return l})),r.d(e,"a",(function(){return u}));var n=r("01ea");var a=r("bc3a"),o=r.n(a);r("5928"),r("ca00");function i(t,e){var r=n["c"]+"solutionRevolvingPlan/getMobPriceSummary";return o.a.get(r,{params:{sessionId:t,trustId:e}}).then((function(t){return Promise.resolve(t.data.result)}))}function s(t,e){var r=n["c"]+"solutionRevolvingPlan/getRepurchaseDiscountRateReferencePI";return o.a.get(r,{params:{taskSessionId:t,trustId:e}}).then((function(t){return Promise.resolve(t.data.result)}))}function c(t,e){var r=n["c"]+"solutionRevolvingPlan/getScenarioRepurchaseInfo";return o.a.get(r,{params:{taskSessionId:t,trustId:e}}).then((function(t){return Promise.resolve(t.data.result)}))}function l(t){var e=n["a"]+"sessionContextResult/getReconcileIndicatorsEx";return o.a.get(e,{params:{sessionId:t}}).then((function(t){return Promise.resolve(t.data.result)}))}function u(t){var e=n["c"]+"ABSMetrics/getMetricsResult";return o.a.get(e,{params:{sessionId:t}}).then((function(t){return Promise.resolve(t.data)}))}},"2b2c":function(t,e,r){},3:function(t,e){},4:function(t,e){},5:function(t,e){},6:function(t,e){},7:function(t,e){},"7e47":function(t,e,r){"use strict";r.r(e);r("e792");var n=r("a026"),a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"app"},[e("div",{staticClass:"app-header"},[e("a",{staticClass:"logo",attrs:{href:"report-form.html"}},[e("img",{attrs:{src:"img/report_form_logo.png",alt:t.title}})]),e("el-button",{attrs:{type:"text"},on:{click:function(e){t.showDialog=!0}}},[e("i",{staticClass:"el-icon-news"}),t._v("\n            报表配置\n        ")])],1),e("div",{staticClass:"app-index"},[e("kendo-spreadsheet",{ref:"spreadsheet",attrs:{columnWidth:100,toolbarData:!1,toolbarInsert:!1,rows:t.rows,columns:450},on:{selectsheet:t.handleSelectSheet}},[t._l(t.reconcilesName,(function(t,r){return e("kendo-spreadsheet-sheet",{key:r,attrs:{name:t}})})),e("kendo-spreadsheet-sheet",{attrs:{name:"IRR分析"}}),e("kendo-spreadsheet-sheet",{attrs:{name:"指标结果查看"}}),t.toCall?e("kendo-spreadsheet-sheet",{attrs:{name:"回购折价率参考(本金)"}}):t._e(),t.toCall?e("kendo-spreadsheet-sheet",{attrs:{name:"回购折价率参考(本息)"}}):t._e(),t.toCall?e("kendo-spreadsheet-sheet",{attrs:{name:"应用回购折价率"}}):t._e()],2)],1),e("el-dialog",{attrs:{top:"10vh",title:"报表配置",visible:t.showDialog},on:{"update:visible":function(e){t.showDialog=e}}},[e("el-tabs",{staticClass:"config-tab",on:{"tab-click":t.handleClick},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},t._l(t.reconciles,(function(r,n){return e("el-tab-pane",{key:n,attrs:{name:n+"",label:t.reconcilesName[n]}},[e("div",{staticClass:"config-switch"},[e("label",[t._v("是否显示每期明细")]),e("el-switch",{attrs:{"active-color":"#ffd100","inactive-color":"#b7b7b7"},model:{value:t.reconcileConfig[r].expandData,callback:function(e){t.$set(t.reconcileConfig[r],"expandData",e)},expression:"reconcileConfig[reconcile].expandData"}})],1),e("el-collapse",{attrs:{accordion:""},model:{value:t.reconcileConfig[r].collapseActive,callback:function(e){t.$set(t.reconcileConfig[r],"collapseActive",e)},expression:"reconcileConfig[reconcile].collapseActive"}},t._l(t.reconcileItem[r],(function(r,n){return e("el-collapse-item",{key:n,attrs:{title:r.itemTitle,name:r.itemInOut}},[e("CheckBoxControl",{attrs:{options:t.itemsName,checkedItem:r.itemData},on:{onChange:function(t){r.itemData=t}}})],1)})),1)],1)})),1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.showDialog=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.submitConfig}},[t._v("确定")])],1)],1)],1)},o=[],i=r("ca00"),s=(r("b841"),r("23a0"));function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function l(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function u(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,f(n.key),n)}}function h(t,e,r){return e&&u(t.prototype,e),r&&u(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function f(t){var e=p(t,"string");return"symbol"==c(e)?e:e+""}function p(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var d=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;l(this,t),this.sheet=e}return h(t,[{key:"clear",value:function(){var t=this.sheet;t.range(0,0,t._rows._count,t._columns._count).clear()}},{key:"cellStyle",value:function(t,e){for(var r in e)t[r](e[r]);return t}},{key:"setTableHead",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=null,a=this.sheet,o=this.cellStyle;return r=Object.assign({bold:!0,textAlign:"center"},r),n="string"===typeof t?a.range(t):a.range.apply(a,t),o(n.merge().value(e),r)}},{key:"setColumnData",value:function(t,e,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,a=this.sheet,o=null,i=null;if("string"===typeof t){var s=t.match(/\d+/)[0],c=t.replace(s,""),l=r.length,u=parseInt(s)+n,h=c+u,f=c+(u+l);o=a.range(t).value(e).bold(!0),i=a.range("".concat(h,":").concat(f)).values(r)}else{var p=t[0],d=t[1],m=r.length-p,g=e.length-d,v=d+g,y=p+n,b=p+m;o=a.range(p,d,1,v).values([e]).bold(!0),i=a.range(y,d,b,v).values(r)}return{titleRange:o,dataRange:i}}},{key:"setRowData",value:function(t,e,r){var n=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,o=this.sheet;if("string"===typeof t){var i=t.match(/\d+/)[0],s=t.replace(i,""),c=r[0].length,l=String.fromCharCode(s.charCodeAt()+a),u=l+i,h=this.createCellPos(s.charCodeAt()-65+a+c)+i;o.range(t).value(e).bold(n).format("#,##0.00_);-#,##0.00;0"),o.range("".concat(u,":").concat(h)).values(r).bold(n).format("#,##0.00_);-#,##0.00;0")}}},{key:"setRowDataAndStyle",value:function(t,e,r){var n=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{bold:!0,textAlign:"center"},o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{bold:!0,textAlign:"right"},i=this.sheet;if("string"===typeof t){var s=t.match(/\d+/)[0],c=t.replace(s,""),l=r[0].length,u=String.fromCharCode(c.charCodeAt()+1),h=u+s,f=this.createCellPos(c.charCodeAt()-65+1+l)+s,p=i.range(t).value(e);for(var d in a)p[d](a[d]);var m=i.range("".concat(h,":").concat(f)).values(r);for(var g in o)m[g](o[g]);n&&m.format("#,##0.00_);-#,##0.00;0")}}},{key:"invertTableData",value:function(t){for(var e=[],r=t.length,n=0,a=0;a<r;a++){n=t[a].length;for(var o=0;o<n;o++){e[o]=[];for(var i=0;i<r;i++)e[o][i]=t[i][o]}}return e}},{key:"createCellPos",value:function(t){var e="A".charCodeAt(0),r="Z".charCodeAt(0),n=r-e+1,a="";while(t>=0)a=String.fromCharCode(t%n+e)+a,t=Math.floor(t/n)-1;return a}},{key:"setCellBorder",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"#ccc",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n={borderLeft:{color:e,size:r},borderRight:{color:e,size:r},borderTop:{color:e,size:r},borderBottom:{color:e,size:r}};this.cellStyle(t,n)}}])}(),m=function(){var t=this,e=t._self._c;return e("div",{staticClass:"checkbox-control"},[e("div",{staticClass:"checkbox-control-head"},[e("el-checkbox",{attrs:{indeterminate:t.isIndeterminate},on:{change:t.handleCheckAllChange},model:{value:t.checkAll,callback:function(e){t.checkAll=e},expression:"checkAll"}},[t._v("全选")]),e("el-input",{staticClass:"search-box",attrs:{placeholder:"请输入关键字",size:"mini"},model:{value:t.filterInput,callback:function(e){t.filterInput=e},expression:"filterInput"}},[e("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"prefix"},slot:"prefix"})])],1),e("div",{staticClass:"checkbox-control-body",style:{height:t.height+"px"}},[e("el-scrollbar",[e("el-checkbox-group",{on:{change:t.handleCheckedChange},model:{value:t.checked,callback:function(e){t.checked=e},expression:"checked"}},t._l(t.filterOptions,(function(r,n){return e("el-tooltip",{attrs:{effect:"dark",content:r,placement:"top"}},[e("el-checkbox",{key:n,attrs:{title:r,label:r}},[t._v("\n                        "+t._s(r)+"\n                    ")])],1)})),1)],1)],1)])},g=[],v={name:"CheckBoxControl",data:function(){return{checkAll:!1,checked:[],filterInput:"",isIndeterminate:!0}},props:{options:{type:Array,default:function(){return[]}},checkedItem:{type:Array,default:function(){return[]}},height:{type:Number,default:280}},watch:{checkedItem:function(t){t.length!==this.checked.length&&(this.checked=t)}},computed:{filterOptions:function(){var t=this.filterInput.trim();return""===t?this.options:this.options.filter((function(e){return-1!==e.indexOf(t)}))}},methods:{handleCheckAllChange:function(t){this.checked=t?this.options:[],this.isIndeterminate=!1,this.emitEvent()},handleCheckedChange:function(t){var e=t.length;this.checkAll=e===this.options.length,this.isIndeterminate=e>0&&e<this.options.length,this.emitEvent()},emitEvent:function(){this.$emit("onChange",this.checked)}},mounted:function(){this.checked=this.checkedItem}},y=v,b=(r("8b35"),r("2877")),w=Object(b["a"])(y,m,g,!1,null,null,null),C=w.exports,S=function(){var t=this,e=t._self._c;return e("div",{staticClass:"checkbox-control checkbox-control-irr"},[e("div",{staticClass:"checkbox-control-head"},[t.isShow?e("div",[t._v("\n            添加合并组\n            "),e("el-input",{staticClass:"search-box",staticStyle:{width:"120px","margin-right":"5px"},attrs:{placeholder:"请输入组合名称",size:"mini"},model:{value:t.groupName,callback:function(e){t.groupName=e},expression:"groupName"}}),e("el-button",{attrs:{size:"mini"},on:{click:t.addgroup}},[t._v("确定合并")])],1):e("div",[t._v("\n            已选中的项目\n        ")])]),e("div",{staticClass:"checkbox-control-body",style:{height:t.height+"px"}},[e("el-scrollbar",[e("el-checkbox-group",{on:{change:t.handleCheckedChange},model:{value:t.checked,callback:function(e){t.checked=e},expression:"checked"}},t._l(t.filterOptions,(function(r,n){return e("el-tooltip",{attrs:{effect:"dark",content:r,placement:"top"}},[e("el-checkbox",{key:n,attrs:{title:void 0!=r.Name?r.Name:r,label:r,disabled:void 0!=r.type}},[t._v("\n                        "+t._s(void 0!=r.Name?r.Name:r)+"\n                    ")])],1)})),1)],1)],1)])},D=[],_={name:"CheckBoxControlForIRR",data:function(){return{checkAll:!1,checked:[],filterInput:"",isIndeterminate:!0,groups:[],groupName:"",isShow:!1}},props:{options:{type:Array,default:function(){return[]}},checkedItem:{type:Array,default:function(){return[]}},height:{type:Number,default:280}},watch:{checkedItem:function(t){t.length!==this.checked.length&&(this.checked=t)},checked:function(t){this.isShow=t.length>0}},computed:{filterOptions:function(){var t=this.filterInput.trim();return""===t?this.options:this.options.filter((function(e){return-1!==e.indexOf(t)}))}},methods:{addgroup:function(){if(this.checked.length>0)if(""!=this.groupName.trim()){for(var t={Name:this.groupName,cashflows:[]},e=0;e<this.checked.length;e++)t.cashflows.push(this.checked[e]);var r={Name:this.groupName,type:0};this.options.push(r),this.groups.push(t),this.checked=[],this.emitEvent(),this.isShow=!1,this.groupName=""}else this.$message("请填写分组名称");else this.$message("请至少选中1个现金流")},handleCheckAllChange:function(t){this.checked=t?this.options:[],this.isIndeterminate=!1,this.emitEvent()},handleCheckedChange:function(t){var e=t.length;this.checkAll=e===this.options.length,this.isIndeterminate=e>0&&e<this.options.length},emitEvent:function(){this.$emit("onChange",this.options,this.groups)}},mounted:function(){this.checked=this.checkedItem}},I=_,x=(r("ec8b"),Object(b["a"])(I,S,D,!1,null,null,null)),k=x.exports,R=r("e382");function P(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=E(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,a=function(){};return{s:a,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,s=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return i=t.done,t},e:function(t){s=!0,o=t},f:function(){try{i||null==r.return||r.return()}finally{if(s)throw o}}}}function N(t){return L(t)||O(t)||E(t)||A()}function A(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function E(t,e){if(t){if("string"==typeof t)return M(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?M(t,e):void 0}}function O(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function L(t){if(Array.isArray(t))return M(t)}function M(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function j(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */j=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,r){return t[e]=r}}function u(t,e,r,n){var o=e&&e.prototype instanceof v?e:v,i=Object.create(o.prototype),s=new N(n||[]);return a(i,"_invoke",{value:x(t,r,s)}),i}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=u;var f="suspendedStart",p="suspendedYield",d="executing",m="completed",g={};function v(){}function y(){}function b(){}var w={};l(w,i,(function(){return this}));var C=Object.getPrototypeOf,S=C&&C(C(A([])));S&&S!==r&&n.call(S,i)&&(w=S);var D=b.prototype=v.prototype=Object.create(w);function _(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function I(t,e){function r(a,o,i,s){var c=h(t[a],t,o);if("throw"!==c.type){var l=c.arg,u=l.value;return u&&"object"==B(u)&&n.call(u,"__await")?e.resolve(u.__await).then((function(t){r("next",t,i,s)}),(function(t){r("throw",t,i,s)})):e.resolve(u).then((function(t){l.value=t,i(l)}),(function(t){return r("throw",t,i,s)}))}s(c.arg)}var o;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return o=o?o.then(a,a):a()}})}function x(e,r,n){var a=f;return function(o,i){if(a===d)throw Error("Generator is already running");if(a===m){if("throw"===o)throw i;return{value:t,done:!0}}for(n.method=o,n.arg=i;;){var s=n.delegate;if(s){var c=k(s,n);if(c){if(c===g)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===f)throw a=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=d;var l=h(e,r,n);if("normal"===l.type){if(a=n.done?m:p,l.arg===g)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(a=m,n.method="throw",n.arg=l.arg)}}}function k(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,k(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var o=h(a,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,g;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function R(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function N(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(R,this),this.reset(!0)}function A(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(B(e)+" is not iterable")}return y.prototype=b,a(D,"constructor",{value:b,configurable:!0}),a(b,"constructor",{value:y,configurable:!0}),y.displayName=l(b,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,l(t,c,"GeneratorFunction")),t.prototype=Object.create(D),t},e.awrap=function(t){return{__await:t}},_(I.prototype),l(I.prototype,s,(function(){return this})),e.AsyncIterator=I,e.async=function(t,r,n,a,o){void 0===o&&(o=Promise);var i=new I(u(t,r,n,a),o);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},_(D),l(D,c,"Generator"),l(D,i,(function(){return this})),l(D,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=A,N.prototype={constructor:N,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(P),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return s.type="throw",s.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],s=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),l=n.call(i,"finallyLoc");if(c&&l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),P(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;P(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:A(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),g}},e}function B(t){return B="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},B(t)}function T(t,e,r,n,a,o,i){try{var s=t[o](i),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,a)}function F(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var o=t.apply(e,r);function i(t){T(o,n,a,i,s,"next",t)}function s(t){T(o,n,a,i,s,"throw",t)}i(void 0)}))}}var G=function(t,e){return i["a"].FloatAdd(t,e)},$={name:"App",mixins:[R["a"]],data:function(){return{title:"智能报表工具",itemsData:{},itemsName:[],reconciles:["Cashflow","LossRecovery","Others"],reconcilesName:["净流入调节表"],reconcileItem:{},reconcileConfig:{},detailItem:[],analyseItem:[],showDialog:!1,activeName:"0",activeIndex:0,records:[],recordBalance:[],recordBalanceItem:[],displayItemsName:[],displayAnalyseItem:[],insuranceDetails:[],insuranncePremiumRate:"0",assetDetails:[],insuranceRevolvingDetails:[],ReportParameters:[],cashflowDaysData:[],CitemIn:{itemInOut:"In",itemTitle:"入项",itemData:[]},CitemOut:{itemInOut:"Out",itemTitle:"出项",itemData:[]},TrustBond:[],InitAssetCashFlowAggregation:[],CalcParameterConfig:null,StressTestSummaryCalcResult:[],rows:2e3,StressedCashflowDetailsData:[],toCall:!1}},components:{CheckBoxControl:C,CheckBoxControlForIRR:k},created:function(){var t=this;return F(j().mark((function e(){var r,n,a,o;return j().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=t.type,n=t.sessionId,t.toCall=1==Number(i["a"].getQueryString("toCall")),a={},o={},t.reconciles.forEach((function(t){a[t]=[{itemInOut:"In",itemTitle:"入项",itemData:[]},{itemInOut:"Out",itemTitle:"出项",itemData:[]}],o[t]={collapseActive:"In",expandData:!1}})),t.reconcileItem=a,t.reconcileConfig=o,!r||!n){e.next=16;break}return t.showLoading("加载现金流计算结果中..."),e.next=10,t.loadCashFlowData(n);case 10:return t.parseCashFlowResultData(),e.next=13,t.renderSheet(t.widget.activeSheet());case 13:t.hideLoading(),e.next=17;break;case 16:window.addEventListener("message",function(){var t=F(j().mark((function t(e){var r;return j().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(r=e.data,"object"!==B(r)||!("__source"in r)){t.next=21;break}return this.sessionId=r.sessionId,this.dataSource=r.dataSource,this.itemsDate=r.itemsDate,this._cashflowDate=r.cashflowDate,this._cashflowData=r.cashflowData,this._cashflowPrincipalData=r.cashflowPrincipalData,this._cashflowSum=r.cashflowSum,this._cashflowPrincipalSum=r.cashflowPrincipalSum,this._shortNameList=r.shortNameList,this._assetsEstimatedValue=r.assetsEstimatedValue,this._totleExpenses=r.totleExpenses,this._lastLoss=r.lastLoss,this._cumulativeIncomeForHighYieldSecurities=r.cumulativeIncomeForHighYieldSecurities,this._riskCost=r.riskCost,this.showLoading(),this.parseDetectorData(r.json),t.next=20,this.renderSheet(this.widget.activeSheet());case 20:this.hideLoading();case 21:case"end":return t.stop()}}),t,this)})));return function(e){return t.apply(this,arguments)}}().bind(t),!1);case 17:case"end":return e.stop()}}),e)})))()},methods:{handleSelectSheet:function(t){var e=this,r=t.sheet;r.isRender||(this.showLoading("生成报表中..."),setTimeout((function(){e.renderSheet(r),e.hideLoading()}),150))},renderSheet:function(t){var e=this;return F(j().mark((function r(){var n,a,o;return j().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(n=e.sessionId,n){r.next=3;break}return r.abrupt("return");case 3:if(a=t._sheetName,console.log(a),!t.isRender){r.next=7;break}return r.abrupt("return");case 7:if(-1===e.reconcilesName.indexOf(a)){r.next=19;break}if(0!==e.records.length){r.next=16;break}return r.next=11,Object(s["e"])(n);case 11:o=r.sent,e.records=o[0],e.recordBalance=o[1].map((function(t){return t.ItemName})),e.recordBalanceItem=o[1].map((function(t){return t.ItemName})),e.reconciles.forEach((function(t){e.reconcileItem[t].forEach((function(r){var n=r.itemInOut,a=[];e.records.forEach((function(r){r.IsInOut===n&&r.ReconcileCategory===t&&(a.push(r.ItemName),"true"===r.ExpandData&&(e.reconcileConfig[t].expandData=!0))})),r.itemData=a}))}));case 16:e.renderSheetReconcile(t),r.next=20;break;case 19:"IRR分析"===a?e.renderSheetScenario(t):"指标结果查看"===a?e.renderItemResult(t):"回购折价率参考(本金)"===a?e.renderDiscountRef(t):"回购折价率参考(本息)"===a?e.renderDiscountPIRef(t):"应用回购折价率"===a&&e.renderRepurchaseDiscountRate(t);case 20:t.isRender=!0;case 21:case"end":return r.stop()}}),r)})))()},transfer:function(t){this.displayItemsName=t,this.analyseItem=t},refreshDataSource:function(t,e){this._cashflowGroups=e},handleClick:function(t){this.activeIndex=parseInt(t.index)},submitConfig:function(){this.showDialog=!1;var t=this.activeIndex,e=this.widget.sheetByIndex(t);t<3&&this.renderSheetReconcile(e)},parseDetectorData:function(t){var e=t.sheets,r=e.length?e[0].rows:[],n=[],a={};r.forEach((function(t){for(var e=[],r="",o=t.cells.length,i=0;i<o;i++){var s=t.cells[i].value;if("ItemName"===s)break;0===i?r=s:e.push(parseFloat(s))}if(""!==r&&!a[r]){var c=e.reduce(G);a[r]={data:e,sum:-1!==r.indexOf("天数")?parseInt(c):parseFloat(c)},n.push(r)}})),this.itemsName=n,this.itemsData=a},parseCashFlowResultData:function(){var t=[],e={};this.dataSource.forEach((function(r){var n=[],a="";for(var o in r){var i=r[o];"ItemName"===o?a=i:n.push(i)}if(""!==a&&!e[a]){var s=n.reduce(G);e[a]={data:n,sum:-1!==a.indexOf("天数")?parseInt(s):parseFloat(s)},t.push(a)}})),this.itemsName=t,this.itemsData=e},getItemData:function(t,e){var r=this,n=[];return t.forEach((function(t){var a=r.itemsData[t];a&&(n.push(a.data.map((function(t){return parseFloat(t)}))),e&&e.call(r,a,t))})),n},getItemDataSummary:function(t){var e=[],r=0,n=[];return this.getItemData(t,(function(t,r){n.push(t.sum),e.push([r,t.sum])})),n.length&&(r=parseFloat(n.reduce(G))),{data:e,summary:r}},getItemDataSum:function(t){for(var e=[],r=0;r<t.length;r++){var n={ItemName:"",Num:0},a=0;for(var o in t[r])"ItemName"!==o&&(a+=t[r][o]);n.ItemName=t[r].ItemName,n.Num=a,e.push(n)}return e},renderSheetStressTestSummaryCalculateResult:function(t){t.columnWidth(0,300),t.columnWidth(1,150);var e=new d(t);if(e.clear(),this.StressTestSummaryCalcResult.length){var r=[],n=[];this.StressTestSummaryCalcResult.forEach((function(t){r.push([t.ItemName]),n.push([t.ItemValue])})),e.setColumnData("A1","名称",r),e.setColumnData("B1","值",n)}},renderSheetReportParameters:function(t){var e=new d(t);e.clear();var r="",n=[],a=[],o=[],i="";this.ReportParameters.length>1&&(console.log(this.ReportParameters),r=this.ReportParameters[0][0].ReportDate.S2Date("yyyy/MM/dd"),n=this.ReportParameters[1]?this.ReportParameters[1]:[],a=this.ReportParameters[2]?this.ReportParameters[2]:[],i=this.ReportParameters[3]&&this.ReportParameters[3][0]?this.ReportParameters[3][0].Multiplier:"暂无数据",o=this.ReportParameters[4]?this.ReportParameters[4]:[]);for(var s=[],c=4,l=65,u=0;l<91;l++,u++)s[u]=String.fromCharCode(l);t.batch((function(){if(t.range("A1").value("日期").bold(!0),t.range("B1").value(r).bold(!0),t.range("A3").value("复投设置").bold(!0),n.length>0){var l=[];for(var u in n[0])switch(u){case"Percentage":l.push("资产配置比例");break;case"LoanRemainingTerm":l.push("资产剩余期限");break;case"InterestRate":l.push("资产利率");break;case"PaymentType":l.push("还款付息方式");break}for(var h=function(t){var r=[];n.forEach((function(e){var n="";switch(l[t]){case"资产配置比例":n=e.Percentage,r.push([n]);break;case"资产剩余期限":n=e.LoanRemainingTerm,r.push([n]);break;case"资产利率":n=e.InterestRate,r.push([n]);break;case"还款付息方式":n=e.PaymentType,r.push([n]);break}})),e.setColumnData(s[t]+"4",l[t],r),c++},f=0;f<l.length;f++)h(f)}c+=3,t.range("A"+c.toString()).input("压力设置").bold(!0);var p=c;if(a.length>0){var d=[];for(var m in a[0])"CurveType"==m?d.unshift("CurveType"):d.push(m);for(var g=function(t){var r=[];a.forEach((function(e){var n="";"CurveType"==d[t]?(n=e.CurveType,r.push([n])):(n=e[d[t]],r.push([n]))})),e.setColumnData(e.createCellPos(t)+(p+1),"",r)},v=0;v<d.length;v++)g(v);t.deleteRow(p)}c+=a.length+1,t.range("A"+(c+1).toString()).value("基于基础情景的倍数设定").bold(!0),t.range("B"+(c+1).toString()).value(i).bold(!0),p+=p+1,t.range("A"+p.toString()).value("循环购买设置").bold(!0),p+=1;var y=[],b=[],w=[],C=[],S=[],D=[],_=[],I=[];o.forEach((function(t){y.push([t.RevolvingPeriodFrom]),b.push([t.RevolvingPeriodTo]),w.push([t.LoanTerm]),C.push([t.LoanRemainingTerm]),S.push([t.InterestRate+"%"]),D.push([t.PaymentType]),_.push([100*t.Percentage+"%"]),I.push([t.ShortName])})),e.setColumnData("A"+p.toString(),"开始期数",y).titleRange.bold(!1),e.setColumnData("B"+p.toString(),"截止期数",b).titleRange.bold(!1),e.setColumnData("C"+p.toString(),"简称",I).titleRange.bold(!1),e.setColumnData("D"+p.toString(),"合同期限",w).titleRange.bold(!1),e.setColumnData("E"+p.toString(),"资产剩余期限",C).titleRange.bold(!1),e.setColumnData("F"+p.toString(),"利率",S).titleRange.bold(!1),e.setColumnData("G"+p.toString(),"还本付息方式",D).titleRange.bold(!1),e.setColumnData("H"+p.toString(),"配置比例",_).titleRange.bold(!1)}),{layout:!0})},renderSheetInsurance:function(t){var e=this,r=new d(t);r.clear(),this.insuranceDetails.length>0&&(this.insuranncePremiumRate=this.insuranceDetails[0][0].InsuranncePremiumRate,this.assetDetails=this.insuranceDetails[1]?this.insuranceDetails[1]:[],this.insuranceRevolvingDetails=this.insuranceDetails[2]?this.insuranceDetails[2]:[]);for(var n=[],a=4,o=65,i=0;o<91;o++,i++)n[i]=String.fromCharCode(o);if(t.range("A1").value("保费费率").bold(!0),t.range("B1").value(this.insuranncePremiumRate).bold(!0).format("##0.0000%"),t.range("A3").value("初始资产保费计算").bold(!0),this.assetDetails.length>0){var s=[];for(var c in this.assetDetails[0])switch(c){case"AccountNo":s.push("资产编号");break;case"RemainingBalance":s.push("可用金额");break;case"CouponRate":s.push("资产利率");break;case"LoanTerm":s.push("资产期数");break}for(var l=function(t){var a=[];e.assetDetails.forEach((function(e){var r="";switch(s[t]){case"资产编号":r=e.AccountNo,a.push([r]);break;case"可用金额":r=e.RemainingBalance,a.push([r]);break;case"资产利率":r=e.CouponRate,a.push([r]);break;case"资产期数":r=e.LoanTerm,a.push([r]);break}})),r.setColumnData(n[t+1]+"4",s[t],a)},u=0;u<s.length;u++)l(u);t.range(n[s.length+1]+"4").input("资产可用金额").bold(!0);for(var h=5;h<this.assetDetails.length+6;h++){var f="C"+h+"/(1+B1*E"+h+"/12)";t.range(n[s.length+1]+h).formula(f).format("#,##0.00_);-#,##0.00;0"),h==this.assetDetails.length+5&&t.range(n[s.length+1]+h).formula("SUM("+(n[s.length+1]+5)+":"+(n[s.length+1]+(h-1))+")").format("￥#,##0.00_);-#,##0.00;0"),a++}t.range(n[s.length+2]+"4").input("保费").bold(!0);for(var p=5;p<this.assetDetails.length+6;p++){var m=n[s.length+1]+p+"*B1*E"+p+"/12";t.range(n[s.length+2]+p).formula(m).format("#,##0.00_);-#,##0.00;0"),p==this.assetDetails.length+5&&t.range(n[s.length+2]+p).formula("SUM("+(n[s.length+2]+5)+":"+(n[s.length+2]+(p-1))+")").format("￥#,##0.00_);-#,##0.00;0").bold(!0)}t.range("B"+a.toString()).input("合计").bold(!0),t.range("C"+a.toString()).formula("SUM(C5:C"+(a-1)+")").format("￥#,##0.00_);-#,##0.00;0")}if(this.insuranceRevolvingDetails.length>0){a+=2,t.range("A"+a.toString()).input("循环购买保费计算").bold(!0);var g=[];for(var v in this.insuranceRevolvingDetails[0])switch(v){case"PeriodsId":g.push("循环购买批次");break;case"ItemValue":g.push("可循环购买金额");break;case"InterestRate":g.push("资产利率");break;case"LoanTerm":g.push("资产期数");break}for(var y=function(t){var o=[];e.insuranceRevolvingDetails.forEach((function(e){var r="";switch(g[t]){case"循环购买批次":r=e.PeriodsId,o.push([r]);break;case"可循环购买金额":r=e.ItemValue,o.push([r]);break;case"资产利率":r=e.InterestRate,o.push([r]);break;case"资产期数":r=e.LoanTerm,o.push([r]);break}})),r.setColumnData(n[t+1]+(a+1),g[t],o)},b=0;b<g.length;b++)y(b);var w=n[g.length+1];t.range(w+(a+1)).input("循环购买资产可用额度").bold(!0);for(var C=a+2,S=C+this.insuranceRevolvingDetails.length,D=C;D<S;D++){var _="C"+D+"/(1+B1*E"+D+"/12)";t.range(w+D).formula(_).format("#,##0.00_);-#,##0.00;0"),D==S-1&&t.range(w+(D+1)).formula("SUM("+(w+C)+":"+(w+(S-1))+")").format("￥#,##0.00_);-#,##0.00;0")}var I=n[g.length+2];t.range(I+(a+1)).input("循环购买保费").bold(!0);for(var x=C;x<S;x++){var k=w+x+"*B1*E"+x+"/12";t.range(I+x).formula(k).format("#,##0.00_);-#,##0.00;0"),x==S-1&&t.range(I+(x+1)).formula("SUM("+(I+C)+":"+(I+(S-1))+")").format("￥#,##0.00_);-#,##0.00;0").bold(!0),a++}t.range("B"+(a+2).toString()).input("合计").bold(!0),t.range("C"+(a+2).toString()).formula("SUM(C"+C+":C"+(S-1)+")").format("￥#,##0.00_);-#,##0.00;0")}},renderSheetReconcile:function(t){var e=this,r=new d(t),n=this;r.clear();var a=3,o=0,s=0,c=0,l=0,u=!1,h=!1,f=null,p=["科目","总计"],m=this.reconcilesName.indexOf(t._sheetName),g=this.reconciles[m],v=this.reconcileConfig[g].expandData,y=function(t){var e=[],a=[],o=r.invertTableData(t),s=n.itemsDate.filter((function(t){return-1!=t.PeriodId}));return o.forEach((function(t,r){var n=s[r].pEndDate,o=""!==n?n.S2Date():"";a.push(o),e.push(parseFloat(t.reduce((function(t,e){return i["a"].FloatAdd(t,e)}))))})),{tableSum:e,periods:a}},b=function(e,n,a,o){for(var i="",s=0;s<e.length;s++)i="SUM("+r.createCellPos(a+s)+(o+1)+":"+r.createCellPos(a+s)+(o+n.length)+")",t.range(r.createCellPos(a+s)+(o+n.length+1)).formula(i).format("#,##0.00_);-#,##0.00;0");for(var c=0;c<n.length+1;c++)i="SUM("+r.createCellPos(a)+(o+1+c)+":"+r.createCellPos(a+e.length-1)+(o+1+c)+")",t.range(r.createCellPos(a-1)+(o+1+c)).formula(i).format("#,##0.00_);-#,##0.00;0")};t.batch((function(){e.reconcileItem[g].forEach((function(n){r.setTableHead("A".concat(a),n.itemTitle);var i=e.getItemDataSummary(n.itemData),d=i.data,m=i.summary,w=e.getItemData(n.itemData),C=y(w),S=C.tableSum,D=C.periods;if(-1!==n.itemInOut.indexOf("In")?(o=m,d.length&&(f=r.setColumnData([a-1,1],p,[].concat(N(d),[["总计",o]])),v&&(S,r.setColumnData([a-1,3],D,N(w)).dataRange.format("#,##0.00_);-#,##0.00;0"),b(D,d,3,a)),a+=d.length,c=a+1)):(s=m,d.length&&(f=r.setColumnData([a-1,1],p,[].concat(N(d),[["总计",s]])),v&&(S,r.setColumnData([a-1,3],D,N(w)).dataRange.format("#,##0.00_);-#,##0.00;0"),b(D,d,3,a)),a+=d.length,l=a+1),u=!0),a+=3,f&&f.dataRange.format("[Green]#,##0.00_);[Red]-#,##0.00;-"),u){if(r.setTableHead("A".concat(a),"入项 - 出项"),v)for(var _=D.length+1,I=2,x=0;x<_;x++){var k=r.createCellPos(I+x)+c+"-"+r.createCellPos(I+x)+l;t.range(r.createCellPos(I+x)+(l+2)).formula(k).format("[Green]#,##0.00_);[Red]-#,##0.00;0.00")}u=!1,h=!0,a+=3}if(h&&"Others"!==g){r.setTableHead("A".concat(a),"余额项");var R=e.recordBalanceItem,P=e.getItemData(R),A=y(P).periods;r.setColumnData([a-1,1],["科目"],R.map((function(t){return[t]}))),r.setColumnData([a-1,3],A,N(P)).dataRange.format("#,##0.00_);-#,##0.00;0")}}))}))},renderSheetDetail:function(t){var e=this,r=new d(t);if(r.clear(),this.itemsDate.length&&t.batch((function(){var t=[],n=[],a=[],o=[],i=[],s=[],c=[];e.itemsDate.forEach((function(e){if(-1!=e.PeriodId){var r=""!==e.pStartDate?e.pStartDate.S2Date():"",l=e.pEndDate?e.pEndDate.S2Date():"",u=e.pDays,h=""!==e.cStartDate?e.cStartDate.S2Date():"",f=""!==e.cEndDate?e.cEndDate.S2Date():"",p=e.cDays;n.push([r]),a.push([l]),o.push([u]),i.push([h]),s.push([f]),c.push([p]),t.push([e.PeriodId.toString()])}})),r.setColumnData("A1","期数",[].concat(t)),r.setColumnData("B1","兑付周期开始日期",n),r.setColumnData("C1","兑付周期结束日期",a),r.setColumnData("D1","兑付周期天数",o),r.setColumnData("E1","归集周期开始日期",i),r.setColumnData("F1","归集周期结束日期",s),r.setColumnData("G1","归集周期天数",c)})),this.detailItem.length){var n=[];this.getItemData(this.detailItem,(function(t){n.push([].concat(N(t.data),[t.sum]))})),r.setColumnData([2,2],this.detailItem,r.invertTableData(n),2).dataRange.format("#,##0.00_);-#,##0.00;0")}},renderSheetScenario:function(t){var e=this,r=new d(t);r.clear();var n=this.reloadDatasource();if(n.length){var a=this;t.batch((function(){t.setDataSource(n),t.range(1,1,n.length,e.analyseItem.length).format("#,##0.00_);-#,##0.00;0"),t.insertRow(1),t.insertColumn(0);var o=Array.apply(null,{length:n.length+1}).map((function(t,e){return[e]}));t.range("A2:A"+(n.length+3)).values(o);var i=n.length+3;t.range("B"+i).input("总回款"),t.range("B"+(i+1)).input("IRR");for(var s=0,c="",l=2,u=0;u<a._columnNames.length;u++)if(0==u){var h=r.createCellPos(u+2);t.range(h+"1").input(a._columnNames[u]),t.range(r.createCellPos(u+1)+"1").input(a._shortNameList[u].ShortName+"日期"),t.range(r.createCellPos(u+1)+"2").input("'"+new Date(a._shortNameList[u].IssueDate).Format("yyyy/MM/dd")),t.insertColumn(parseInt(u+3)),t.range(r.createCellPos(u+3)+"1").input(a._columnNames[u]+" - PV"),t.columnWidth(u+3,200);for(var f=0;f<n.length+2;f++)if(c=r.createCellPos(l)+(f+2)+"/(1+",c+=r.createCellPos(l)+(i+1)+")^((",c+="B"+(f+2)+"-B2)/365)",t.range(r.createCellPos(u+3)+(f+2)).formula(c),t.range(r.createCellPos(u+3)+(f+2)).color("red"),f==n.length+1){var p="SUM("+r.createCellPos(u+3)+"2:"+r.createCellPos(u+3)+(f+1)+")";t.range(r.createCellPos(u+3)+(f+2)).formula(p),t.range(r.createCellPos(u+3)+(f+2)).color("red")}s=parseInt(u+2);var d=h+"3:"+h+(n.length+2),m="SUM("+d+")";t.range(r.createCellPos(u+2)+i).formula(m).format("#,##0.00_);-#,##0.00;0");var g="XIRR("+h+"2:"+h+(i-1)+",B2:B"+(i-1)+",-0.999999)",v="XIRR("+h+"2:"+h+(i-1)+",B2:B"+(i-1)+",0.5)",y="IFERROR("+g+","+v+")";t.range(r.createCellPos(u+2)+(i+1)).formula(y),t.range(r.createCellPos(u+2)+(i+1)).format("##0.0000%")}else{var b=r.createCellPos(s+3);t.range(r.createCellPos(s+3)+"1").input(a._columnNames[u]),t.range(r.createCellPos(s+2)+"1").input(a._shortNameList[u].ShortName+"日期"),t.columnWidth(s+2,100),t.range(r.createCellPos(s+2)+"2").input("'"+new Date(a._shortNameList[u].IssueDate).Format("yyyy/MM/dd")),t.insertColumn(s+4),t.range(r.createCellPos(s+4)+"1").input(a._columnNames[u]+" - PV"),t.columnWidth(s+4,200);for(var w=0;w<n.length+2;w++)if(c=r.createCellPos(s+3)+(w+2)+"/(1+",c+=r.createCellPos(s+3)+(i+1)+")^((",c+=r.createCellPos(s+2)+(w+2)+"-"+r.createCellPos(s+2)+"2)/365)",t.range(r.createCellPos(s+4)+(w+2)).formula(c),t.range(r.createCellPos(s+4)+(w+2)).color("red"),w==n.length+1){var C="SUM("+(r.createCellPos(s+4)+2)+":"+(r.createCellPos(s+4)+(w+1))+")";console.log(C),t.range(r.createCellPos(s+4)+(w+2)).formula(C),t.range(r.createCellPos(s+4)+(w+2)).color("red")}var S=b+"3:"+b+(n.length+2),D="SUM("+S+")";t.range(r.createCellPos(s+3)+i).formula(D).format("#,##0.00_);-#,##0.00;0");var _="XIRR("+b+"2:"+b+(i-1)+",B2:B"+(i-1)+",-0.999999)",I="XIRR("+b+"2:"+b+(i-1)+",B2:B"+(i-1)+",0.5)",x="IFERROR("+_+","+I+")";t.range(r.createCellPos(s+3)+(i+1)).formula(x),t.range(r.createCellPos(s+3)+(i+1)).format("##0.0000%"),s+=3}s=0;for(var k=0;k<e._shortNameList.length;k++)0==k?(t.range(r.createCellPos(k+2)+2).input("-"+e._shortNameList[k].OfferAmount),s+=2):(t.range(r.createCellPos(s+3)+2).input("-"+e._shortNameList[k].OfferAmount),s+=3)}))}},renderItemResult:function(t){var e=new d(t);e.clear(),Object(s["a"])(this.sessionId).then((function(r){if(0!==r.length){var n=r[0],a=r[1];t.batch((function(){e.setRowDataAndStyle("A1","期数",[n[0].period],!1),e.setRowDataAndStyle("A2","日期",[n[1].date]),e.setRowDataAndStyle("A3","apr",[n[2].apr]),e.setRowDataAndStyle("A4","分子",[n[3].numerator]),e.setRowDataAndStyle("A5","分母",[n[4].denominator]),e.setRowDataAndStyle("A7","科目",[["日期（到期）","irr","实收利率"]]);for(var t=0;t<a.length;t++)e.setRowDataAndStyle("A"+(8+t),a[t].assetType,[[a[t].date,a[t].irr,a[t].rir]])}))}}))},renderSheetRevolvingPurchase:function(t){var e=this,r=new d(t);r.clear();var n=[],a=0,o=0,i=0;function s(t){var e=[];return t.forEach((function(t){e.push([parseFloat(t.AccountNo)]),0==a&&(a=Object.keys(t).length)})),e}function c(t){for(var e,r=[],n={},o=function(a){n={},n.period=a,n.amount=[],t.forEach((function(t,r){e=r>=a?null==t[a]?"":t[a]:null==t[a]?0:t[a],n.amount.push([e])})),r.push(n)},i=-1;i<a-2;i++)o(i);return r}function l(e,n){var i,s,c,l,u,h=n-1,f=String.fromCharCode(67);t.range("A"+h).value("XIRR").bold(!0);for(var p=0;p<o;p++){f=r.createCellPos(2+p),l=p+n,u=r.createCellPos(a);var d="XIRR("+f+l+":"+u+l+","+f+"2:"+u+"2, -0.999999)",m="XIRR("+f+l+":"+u+l+","+f+"2:"+u+"2, 0.5)",g="IFERROR("+d+","+m+")";t.range("A"+l).formula(g).format("##0.0000%")}t.range("A"+(o+n)).value("合计").bold(!0);for(var v=0;v<a-1;v++)f=r.createCellPos(2+v),i=f+(n+1),s=f+(o+h),c="SUM("+i+":"+s+")",t.range(f+(o+n)).formula(c).format("#,##0.00_);-#,##0.00;0");i="C"+(o+n),s=f+(o+n),c="SUM("+i+":"+s+")",t.range("B"+(o+n)).formula(c).format("#,##0.00_);-#,##0.00;0")}function u(t){var e=[],r={};return t.forEach((function(t){for(var n in r={},r.period=[],r.amount=[],t)r.period.push(n),r.amount.push(t[n]);e.push(r)})),e}function h(e,n){var a,i,s,c,l;t.range("C"+(o+n+4)).value("参考值").bold(!0),t.range("C"+(o+n+5)).value("差值").bold(!0);for(var u=0;u<e.period.length-1;u++)a=r.createCellPos(4+u),i=a+(n+1),s=u<o-1?a+(n+1+u):a+(n+o-1),c="SUM("+i+":"+s+")",t.range(a+(o+n+4)).formula(c).format("#,##0.00_);-#,##0.00;0"),l=a+(o+n+3)+"-"+a+(o+n+4),t.range(a+(o+n+5)).formula(l).format("#,##0.000_);-#,##0.000;0")}this._cashflowDate.forEach((function(t){if(null!=t.EndDate){var e=t.EndDate.S2Date();n.push(e)}})),t.batch((function(){if(t.range("A1").value("本金和利息").bold(!0),r.setRowData("B2","循环购买日期",[n]),e._cashflowData.length){var a=[],f=[];o=e._cashflowData.length,a=s(e._cashflowData),f=c(e._cashflowData),r.setColumnData("B3","循环购买次数",a);for(var p=0;p<f.length;p++){var d=r.createCellPos(p+2);r.setColumnData(d+"3",p-1,f[p].amount).dataRange.format("#,##0.00_);-#,##0.00;0")}l(e._cashflowData,4)}if(e._cashflowSum.length){var m=u(e._cashflowSum);r.setRowData("C"+(o+6),"",[m[0].period],!0),r.setRowData("C"+(o+7),"",[m[0].amount],!1),h(m[0],4)}if(t.range("A"+(o+10)).value("只含本金").bold(!0),r.setRowData("B"+(o+11),"循环购买日期",[n]),e._cashflowPrincipalData.length){var g=[],v=[];i=e._cashflowPrincipalData.length,g=s(e._cashflowPrincipalData),v=c(e._cashflowPrincipalData),r.setColumnData("B"+(o+12),"循环购买次数",g);for(var y=0;y<v.length;y++){var b=r.createCellPos(y+2);r.setColumnData(b+(o+12),y-1,v[y].amount).dataRange.format("#,##0.00_);-#,##0.00;0")}l(e._cashflowPrincipalData,o+13)}if(e._cashflowPrincipalSum.length){var w=u(e._cashflowPrincipalSum);r.setRowData("C"+(o+i+15),"",[w[0].period]),r.setRowData("C"+(o+i+16),"",[w[0].amount],!1),h(w[0],o+13)}}))},renderSheetCapitalOutflowEstimate:function(t){var e=this,r=new d(t);r.clear();var n=0,a=[],o=[],i=this.reconcileConfig["Cashflow"].expandData,s=this.itemsDate.filter((function(t){return-1!=t.PeriodId}));s.forEach((function(t,e){var r=t.pEndDate,n=""!==r?r.S2Date():"";o.push([n])})),t.batch((function(){r.setColumnData("A1","日期",o),e.reconcileItem["Cashflow"].forEach((function(t,o){a=[],t.itemData.forEach((function(t,e){a.push(t)}));var s=e.getItemDataSummary(t.itemData),c=s.data,l=(s.summary,e.getItemData(t.itemData)),u=r.invertTableData(l);c.length&&(i&&r.setColumnData([0,n+1],a,N(u)).dataRange.format("#,##0.00_);-#,##0.00;0"),n+=c.length)}))}))},reloadDatasource:function(){var t=this,e=this.analyseItem;this.sessionId;this._columnNames=[],this._cashflowGroups=[],this._displayArray=this.dataSource.filter((function(t){var r=JSON.stringify(e);if(r.includes(t.ItemName))return t}));for(var r=this._shortNameList.map((function(t){return t.ShortName})),n=[],a=0;a<r.length;a++){var o={Name:r[a]+"（本金分配+利息分配）",cashflows:[]};this.displayItemsName.push(r[a]+"（本金分配+利息分配）");for(var i=0;i<this.dataSource.length;i++)(this.dataSource[i].ItemName.indexOf(r[a]+"当期利息分配")>-1||this.dataSource[i].ItemName.indexOf(r[a]+"当期本金分配")>-1)&&(n.push(this.dataSource[i]),o.cashflows.push(this.dataSource[i].ItemName));this._cashflowGroups.push(o)}if(!(this._displayArray.length>24)){if(this._cashflowGroups.length>0)for(var s=function(e){for(var r=[],n=function(n){var a=t.dataSource.filter((function(r){if(t._cashflowGroups[e].cashflows[n]==r.ItemName)return r}))[0];r.push(a)},a=0;a<t._cashflowGroups[e].cashflows.length;a++)n(a);var o={};for(var i in r[0]){var s=0;if("ItemName"!=i){o[i]=0;for(var c=0;c<r.length;c++)s+=parseFloat(r[c][i])}o[i]=s.toString()}o["ItemName"]="'"+t._cashflowGroups[e].Name,t._displayArray.push(o)},c=0;c<this._cashflowGroups.length;c++)s(c);var l=[],u={},h="日期";u[h]="";for(var f=0;f<this._displayArray.length;f++){var p=String.fromCharCode(64+parseInt(f+1))+this._displayArray[f].ItemName;u[p]="",this._columnNames.push(this._displayArray[f].ItemName)}var d=this.itemsDate.slice(0);for(var m in d.shift(),this._displayArray[0]){var g=JSON.parse(JSON.stringify(u));for(var v in g)v.substring(1)===this._displayArray[0].ItemName&&(g[v]=this._displayArray[0][m],l.push(g))}l.shift();for(var y=1;y<this._displayArray.length;y++)for(var b=Object.getOwnPropertyNames(this._displayArray[y]).length-1,w=0;w<b;w++)for(var C in l[w])if(C.substring(1)===this._displayArray[y].ItemName){var S="Periods"+w;l[w][C]=this._displayArray[y][S]}for(var D=0;D<l.length;D++)for(var _ in l[D]["日期"]=d[D].pEndDate.S2Date("yyyy/MM/dd"),l[D])if("日期"!=_){var I=_.substring(0,1);l[D][I]=parseFloat(l[D][_]),delete l[D][_]}for(var x=[],k=0;k<l.length;k++){var R={};for(var P in l[k])if("日期"!=P){var N=P.substring(0,1)+"日期";R[N]=l[k]["日期"],R[P]=l[k][P]}x.push(R)}return x}this.$message("IRR渲染较耗内存，请不要选择超过24条现金流")},reloadAmortizeDatasource:function(){var t=this,e=this.analyseItem;this.sessionId;this._columnNames=[],this._displayArray=this.dataSource.filter((function(t){var r=JSON.stringify(e);if(r.includes(t.ItemName))return t}));var r=[];this._shortNameList.forEach((function(t){r.push(t.ShortName)}));for(var n=function(e){t._displayArray=t._displayArray.concat(t.dataSource.filter((function(t){if(t.ItemName.indexOf(r[e]+"当期利息分配")>-1||t.ItemName.indexOf(r[e]+"当期本金分配")>-1)return t})))},a=0;a<r.length;a++)n(a);if(!(this._displayArray.length>24)){var o=[],i={},s="日期";i[s]="";for(var c=0;c<this._displayArray.length;c++){var l=String.fromCharCode(64+parseInt(c+1))+this._displayArray[c].ItemName,u=l;i[u]="",this._columnNames.push(this._displayArray[c].ItemName)}var h=this.itemsDate.slice(0);for(var f in h.shift(),this._displayArray[0]){var p=JSON.parse(JSON.stringify(i));for(var d in p)d.substring(1)==this._displayArray[0].ItemName&&(p[d]=this._displayArray[0][f],o.push(p))}o.shift();for(var m=1;m<this._displayArray.length;m++)for(var g=Object.getOwnPropertyNames(this._displayArray[m]).length-1,v=0;v<g;v++)for(var y in o[v])if(y.substring(1)==this._displayArray[m].ItemName){var b="Periods"+v;o[v][y]=this._displayArray[m][b]}for(var w=0;w<o.length;w++)for(var C in o[w]["日期"]=h[w].pEndDate.S2Date("yyyy/MM/dd"),o[w])if("日期"!=C){var S=C.substring(0,1);o[w][S]=parseFloat(o[w][C]),delete o[w][C]}return o}this.$message("IRR渲染较耗内存，请不要选择超过24条现金流")},renderDiscountRef:function(t){var e=this;return F(j().mark((function r(){var n,a,o,c,l,u,h,f,p,m,g,v,y,b,w,C,S,D,_,I,x;return j().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(n=new d(t),n.clear(),a=i["a"].getQueryString("trustId"),a){r.next=5;break}return r.abrupt("return",e.$message.error("缺失参数"));case 5:return r.next=7,Object(s["b"])(e.sessionId,a);case 7:if(o=r.sent,0!==o.length){r.next=10;break}return r.abrupt("return");case 10:c=function(t){return null!=t&&void 0!=t?Number(i["a"].floatMul(t,100)):t},l=o[0],u=o[1],h=[],l&&l.length&&h.push(["回购起算日",l[0].repurchaseCalculationDate]),h.push(["资产分类","回购起算日资产余额","回购价格(本金)","回购折价率(本金)(%)"]),f=P(l);try{for(f.s();!(p=f.n()).done;)m=p.value,h.push([m.assetCategory,m.assetBalance,m.repurchasePrice,c(m.discountRate)])}catch(k){f.e(k)}finally{f.f()}g=t.range("A1:D".concat(h.length)),g.values(h),n.setCellBorder(g),t.range("B3:C".concat(h.length)).format("#,##0.00_);-#,##0.00;0"),t.range("D3:D".concat(h.length)).format("##0.0000"),v=h.length+3,y=["M0","M1","M2","M3","M4","M5","M6","M6plus"],h=[["资产分组","账龄（月）"].concat(N(y.map((function(t){return"回购起算日资产余额"}))),N(y.map((function(t){return"回购时匹配的静态池回收率(%)"}))),N(y.map((function(t){return"回购价格"})))),["",""].concat(y,y,y)],b=P(u),r.prev=27,C=j().mark((function t(){var e;return j().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e=w.value,h.push([e.groupId,e.seasoning].concat(N(y.map((function(t){return e[t.toLowerCase()]}))),N(y.map((function(t){return c(e["pricing"+t])}))),N(y.map((function(t){return e["price"+t]})))));case 2:case"end":return t.stop()}}),t)})),b.s();case 30:if((w=b.n()).done){r.next=34;break}return r.delegateYield(C(),"t0",32);case 32:r.next=30;break;case 34:r.next=39;break;case 36:r.prev=36,r.t1=r["catch"](27),b.e(r.t1);case 39:return r.prev=39,b.f(),r.finish(39);case 42:for(S=t.range("A".concat(v,":Z").concat(v+h.length-1)),S.values(h),n.setCellBorder(S),t.range("C".concat(v+2,":J").concat(v+2+h.length-1)).format("#,##0.00_);-#,##0.00"),t.range("K".concat(v+2,":R").concat(v+2+h.length-1)).format("##0.0000"),t.range("S".concat(v+2,":Z").concat(v+2+h.length-1)).format("#,##0.00_);-#,##0.00"),t.range("A".concat(v,":A").concat(v+1)).merge(),t.range("B".concat(v,":B").concat(v+1)).merge(),D=y.length,_=1;_<=3;_++)I=n.createCellPos(2+_*D-D),x=n.createCellPos(2+_*D-1),t.range("".concat(I).concat(v,":").concat(x).concat(v)).merge().textAlign("center");case 52:case"end":return r.stop()}}),r,null,[[27,36,39,42]])})))()},renderDiscountPIRef:function(t){var e=this;return F(j().mark((function r(){var n,a,o,c,l,u,h,f,p,m,g;return j().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(n=new d(t),n.clear(),a=i["a"].getQueryString("trustId"),a){r.next=5;break}return r.abrupt("return",e.$message.error("缺失参数"));case 5:return r.next=7,Object(s["c"])(e.sessionId,a);case 7:if(o=r.sent,0!==o.length){r.next=10;break}return r.abrupt("return");case 10:c=function(t){return null!=t&&void 0!=t?Number(i["a"].floatMul(t,100)):t},l=o,u=[],l&&l.length&&u.push(["回购起算日",new Date(l[0].repurchaseCalculationDate).Format("yyyy-MM-dd")]),u.push(["资产分类","回购起算日资产余额","回购价格(本息)","回购折价率(本息)(%)"]),h=P(l);try{for(h.s();!(f=h.n()).done;)p=f.value,u.push([p.assetCategory,p.assetBalance,p.repurchasePrice,c(p.discountRate)])}catch(v){h.e(v)}finally{h.f()}m=t.range("A1:D".concat(u.length)),m.values(u),n.setCellBorder(m),t.range("B3:C".concat(u.length)).format("#,##0.00_);-#,##0.00;0"),t.range("D3:D".concat(u.length)).format("##0.0000"),g=3;while(g>0)t.columnWidth(g,130),g--;case 24:case"end":return r.stop()}}),r)})))()},renderRepurchaseDiscountRate:function(t){var e=this;return F(j().mark((function r(){var n,a,o,c,l,u,h,f,p,m;return j().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(n=new d(t),n.clear(),a=i["a"].getQueryString("trustId"),a){r.next=5;break}return r.abrupt("return",e.$message.error("缺失参数"));case 5:return r.next=7,Object(s["d"])(e.sessionId,a);case 7:if(o=r.sent,0!==o.length){r.next=10;break}return r.abrupt("return");case 10:c=o,l=[],c&&c.length&&(l.push(["回购起算日",c[0].repurchase_date]),l.push(["回购基准",c[0].repurchase_price_basis])),l.push(["资产分类","回购起算日资产余额","回购起算日资产利息余额(预测)","回购价格(本金)","回购价格(本息)","回购折价率(本金)(%)","回购折价率(本息)(%)"]),u=P(c);try{for(u.s();!(h=u.n()).done;)f=h.value,l.push([f.asset_category,f.balance,f.forecasted_interest,f.price,f.price_pi,f.discount_rate,f.discount_rate_pi])}catch(g){u.e(g)}finally{u.f()}p=t.range("A1:G".concat(l.length)),p.values(l),n.setCellBorder(p),t.range("B4:E".concat(l.length)).format("#,##0.00_);-#,##0.00;0"),t.range("F4:G".concat(l.length)).format("##0.0000"),m=6;while(m>0)t.columnWidth(m,130),m--;case 23:case"end":return r.stop()}}),r)})))()}}},U=$,H=(r("8e0b"),Object(b["a"])(U,a,o,!1,null,null,null)),Y=H.exports,z=r("5c96"),J=r.n(z),V=(r("e3824"),r("c2b1"),r("0fae"),r("0e30"),r("935e"),r("c3e9"),r("ff86"));window.JSZip=r("7c39"),n["default"].config.devtools=!1,n["default"].use(V["SpreadsheetInstaller"]),n["default"].config.productionTip=!1,n["default"].use(J.a,{size:"small",zIndex:100}),new n["default"]({render:function(t){return t(Y)}}).$mount("#app")},8:function(t,e){},"804c":function(t,e,r){},"883e":function(t,e,r){},"8b35":function(t,e,r){"use strict";r("2b2c")},"8e0b":function(t,e,r){"use strict";r("883e")},9:function(t,e){},b841:function(t,e){Date.prototype.Format=function(t){var e={"M+":this.getMonth()+1,"d+":this.getDate(),"h+":this.getHours()%12==0?12:this.getHours()%12,"H+":this.getHours(),"m+":this.getMinutes(),"s+":this.getSeconds(),"q+":Math.floor((this.getMonth()+3)/3),S:this.getMilliseconds()},r={0:"日",1:"一",2:"二",3:"三",4:"四",5:"五",6:"六"};for(var n in/(y+)/.test(t)&&(t=t.replace(RegExp.$1,(this.getFullYear()+"").substr(4-RegExp.$1.length))),/(E+)/.test(t)&&(t=t.replace(RegExp.$1,(RegExp.$1.length>1?RegExp.$1.length>2?"星期":"周":"")+r[this.getDay()+""])),e)new RegExp("("+n+")").test(t)&&(t=t.replace(RegExp.$1,1==RegExp.$1.length?e[n]:("00"+e[n]).substr((""+e[n]).length)));return t},Date.prototype.Diff=function(t,e){if(!(arguments.length<2||e.constructor!=Date))switch(t){case"s":return parseInt((e-this)/1e3);case"n":return parseInt((e-this)/6e4);case"h":return parseInt((e-this)/36e5);case"d":return parseInt((e-this)/864e5);case"w":return parseInt((e-this)/6048e5);case"m":return e.getMonth()+1+12*(e.getFullYear()-this.getFullYear())-(this.getMonth()+1);case"y":return e.getFullYear()-this.getFullYear();default:return}},String.prototype.Format=function(){var t=arguments;return this.replace(/{(\d+)}/g,(function(e,r){return"undefined"!=typeof t[r]?t[r]:e}))},String.prototype.S2Date=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"yyyy-MM-dd",e=!1;return e?new Date(parseInt(this.replace(/[^\d+]/g,""))).Format(t):new Date(this.replace(/\s\d+(:\d+){2}/,"")).Format(t)},Number.prototype.S2Date=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"yyyy-MM-dd",e=!1;return e?new Date(parseInt(this.replace(/[^\d+]/g,""))).Format(t):new Date(this).Format(t)},Array.prototype.Remove=function(t){var e=this.indexOf(t);e>-1&&this.splice(e,1)}},c2b1:function(t,e,r){},ca00:function(t,e,r){"use strict";e["a"]={isObject:function(t){return"[object object]"===Object.prototype.toString.call(t).toLowerCase()},isNull:function(t){return"[object null]"===Object.prototype.toString.call(t).toLowerCase()},isArray:function(t){return"[object Array]"===Object.prototype.toString.call(t).toLowerCase()},htmlEncodeDom:function(t){var e=document.createElement("span");return e.appendChild(document.createTextNode(t)),e.innerHTML},htmlDecodeDom:function(t){var e=document.createElement("span");return e.innerHTML=t,e.textContent},getQueryString:function(t){var e=new RegExp("(^|&)"+t+"=([^&]*)(&|$)","i"),r=window.location.search.substr(1).match(e);return null!=r?unescape(r[2]):null},getQueryStringByString:function(t,e){var r=new RegExp("(^|&)"+t+"=([^&]*)(&|$)","i"),n=e.substr(1).match(r);return null!=n?decodeURI(n[2]):null},physicalFileDownload:function(t){var e=document.createElement("iframe");e.src=decodeURI(encodeURI(t)),e.style.display="none",document.body.appendChild(e)},floatMul:function(t,e){t=t||0,e=e||0;var r=0,n=t.toString(),a=e.toString();try{r+=n.split(".")[1].length}catch(o){}try{r+=a.split(".")[1].length}catch(o){}return Number(n.replace(".",""))*Number(a.replace(".",""))/Math.pow(10,r)},floatSub:function(t,e){var r,n,a,o;try{r=t.toString().split(".")[1].length}catch(i){r=0}try{n=e.toString().split(".")[1].length}catch(i){n=0}return a=Math.pow(10,Math.max(r,n)),o=r>=n?r:n,((t*a-e*a)/a).toFixed(o)},floatDiv:function(t,e){var r,n,a=0,o=0;try{a=t.toString().split(".")[1].length}catch(i){a=0}try{o=e.toString().split(".")[1].length}catch(i){o=0}return r=Number(t.toString().replace(".","")),n=Number(e.toString().replace(".","")),this.floatMul(r/n,Math.pow(10,o-a))},FloatAdd:function(t,e){var r,n,a,o;t=t||0,e=e||0;try{r=t.toString().split(".")[1].length}catch(s){r=0}try{n=e.toString().split(".")[1].length}catch(s){n=0}if(o=Math.abs(r-n),a=Math.pow(10,Math.max(r,n)),o>0){var i=Math.pow(10,o);r>n?(t=Number(t.toString().replace(".","")),e=Number(e.toString().replace(".",""))*i):(t=Number(t.toString().replace(".",""))*i,e=Number(e.toString().replace(".","")))}else t=Number(t.toString().replace(".","")),e=Number(e.toString().replace(".",""));return(t+e)/a}}},e382:function(t,e,r){"use strict";var n=r("ca00"),a=(r("23a0"),r("01ea")),o=r("bc3a"),i=r.n(o);function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",l=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function h(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{h({},"")}catch(t){h=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var o=e&&e.prototype instanceof b?e:b,i=Object.create(o.prototype),s=new E(n||[]);return a(i,"_invoke",{value:R(t,r,s)}),i}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var d="suspendedStart",m="suspendedYield",g="executing",v="completed",y={};function b(){}function w(){}function C(){}var S={};h(S,i,(function(){return this}));var D=Object.getPrototypeOf,_=D&&D(D(O([])));_&&_!==r&&n.call(_,i)&&(S=_);var I=C.prototype=b.prototype=Object.create(S);function x(t){["next","throw","return"].forEach((function(e){h(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function r(a,o,i,c){var l=p(t[a],t,o);if("throw"!==l.type){var u=l.arg,h=u.value;return h&&"object"==s(h)&&n.call(h,"__await")?e.resolve(h.__await).then((function(t){r("next",t,i,c)}),(function(t){r("throw",t,i,c)})):e.resolve(h).then((function(t){u.value=t,i(u)}),(function(t){return r("throw",t,i,c)}))}c(l.arg)}var o;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return o=o?o.then(a,a):a()}})}function R(e,r,n){var a=d;return function(o,i){if(a===g)throw Error("Generator is already running");if(a===v){if("throw"===o)throw i;return{value:t,done:!0}}for(n.method=o,n.arg=i;;){var s=n.delegate;if(s){var c=P(s,n);if(c){if(c===y)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===d)throw a=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=g;var l=p(e,r,n);if("normal"===l.type){if(a=n.done?v:m,l.arg===y)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(a=v,n.method="throw",n.arg=l.arg)}}}function P(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,P(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var o=p(a,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,y;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function N(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function A(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(N,this),this.reset(!0)}function O(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(s(e)+" is not iterable")}return w.prototype=C,a(I,"constructor",{value:C,configurable:!0}),a(C,"constructor",{value:w,configurable:!0}),w.displayName=h(C,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,C):(t.__proto__=C,h(t,u,"GeneratorFunction")),t.prototype=Object.create(I),t},e.awrap=function(t){return{__await:t}},x(k.prototype),h(k.prototype,l,(function(){return this})),e.AsyncIterator=k,e.async=function(t,r,n,a,o){void 0===o&&(o=Promise);var i=new k(f(t,r,n,a),o);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},x(I),h(I,u,"Generator"),h(I,i,(function(){return this})),h(I,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=O,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(A),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return s.type="throw",s.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],s=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),l=n.call(i,"finallyLoc");if(c&&l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),A(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;A(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:O(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),y}},e}function l(t,e,r,n,a,o,i){try{var s=t[o](i),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,a)}function u(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var o=t.apply(e,r);function i(t){l(o,n,a,i,s,"next",t)}function s(t){l(o,n,a,i,s,"throw",t)}i(void 0)}))}}e["a"]={data:function(){return{type:"simplify",sessionId:"",dataSource:[],itemsDate:[],loading:null}},computed:{procName:function(){return"complete"===this.type?"task.usp_GetCashFlowRunResultBySessionId":"simplify"===this.type?"task.usp_GetCashFlowRunResultBySessionId_Simplified":void 0},widget:function(){return this.$refs.spreadsheet?this.$refs.spreadsheet.kendoWidget():null}},created:function(){var t=this.getUrlParams();t&&(this.type=t.type,this.sessionId=t.sessionId),this._shortNameList=[],this._cashflowDate=[],this._cashflowData=[],this._cashflowSum=[],this._cashflowPrincipalData=[],this._cashflowPrincipalSum=[],this._assetsEstimatedValue=[]},methods:{getUrlParams:function(){var t=n["a"].getQueryString("type"),e=n["a"].getQueryString("SessionId");return t&&e?{type:t,sessionId:e}:null},loadCashFlowData:function(t){var e=this;return u(c().mark((function r(){var n;return c().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return n=a["a"]+"sessionContextResult/getCashFlowRunResultBySessionIdSimplified",r.next=3,i.a.get(n,{params:{sessionId:t}}).then((function(t){t.data.result.length?(t=t.data.result,e.dataSource=t[0],e.itemsDate=t[1]?t[1]:[],e._cashflowDate=t[2]?t[2]:[],e._cashflowData=t[3]?t[3]:[],e._cashflowPrincipalData=t[4]?t[4]:[],e._cashflowSum=t[5]?t[5]:[],e._cashflowPrincipalSum=t[6]?t[6]:[],e._shortNameList=t[7]?t[7]:[],e._assetsEstimatedValue=t[8]?t[8]:[],e._totleExpenses=t[9]?t[9]:[],e._lastLoss=t[10]?t[10]:[],e._cumulativeIncomeForHighYieldSecurities=t[11]?t[11]:[],e._riskCost=t[12]?t[12]:[],console.log(t)):e.$message("抱歉，什么也没有找到~")}));case 3:case"end":return r.stop()}}),r)})))()},resize:function(){var t=this;this.$nextTick((function(){t.widget.resize()}))},showLoading:function(t){this.loading?(this.loading.visible=!0,this.updateLoading(t)):this.loading=this.$loading({lock:!0,text:t||"加载中",background:"rgba(255, 255, 255, 0.8)"})},updateLoading:function(t){this.loading&&this.$set(this.loading,"text",t)},hideLoading:function(){this.loading.visible=!1},removeLoading:function(){this.loading.close()}}}},e3824:function(t,e,r){},ec8b:function(t,e,r){"use strict";r("804c")}});