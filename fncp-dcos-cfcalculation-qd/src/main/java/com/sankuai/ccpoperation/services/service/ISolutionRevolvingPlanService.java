/*
 * Copyright (c) 2024 Sankuai, Inc. All Rights Reserved.
 */
package com.sankuai.ccpoperation.services.service;

import com.sankuai.ccpoperation.services.constant.ResultInfo;
import com.sankuai.ccpoperation.services.entity.SolutionRevolvingPlan;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ISolutionRevolvingPlanService extends IService<SolutionRevolvingPlan> {

    ResultInfo getSolutionRevolvingPool(String trustId);

    ResultInfo getSolutionSettingCmb(String trustId);

    ResultInfo getScenarios(String trustId);

    ResultInfo getExtSetting(String trustId);

    ResultInfo getPortfolioPaymentSequence(String trustId);

    ResultInfo getBondPremiumSettings(String trustId);

    ResultInfo getTrustBondExtraBpSettings(String trustId);

    ResultInfo getFloatRateSettings(String trustId);

    ResultInfo getSolutionSetting(String trustId);

    /**
     * 获取回购价格明细数据
     *
     * @param sessionId 会话ID
     * @return 回购价格明细数据
     */
    ResultInfo getMobPriceSummary(String sessionId);

    /**
     * 获取产品的所有场景测算回购折价率计算结果
     *
     * @param page 分页参数
     * @param trustId 产品ID
     * @return 回购折价率计算结果
     */
    ResultInfo getAllScenarioMobPriceSummary(Page<Map<String, Object>> page, Long trustId);

    /**
     * 获取业务要求和回购价格基准
     * @param trustId 产品ID
     * @return 业务要求和回购价格基准
     */
    ResultInfo getBusinessRule(Long trustId);

    /**
     * 获取回购折价率调优计算过的日期清单
     * @param trustId 产品ID
     * @return 日期清单
     */
    ResultInfo getRepurchaseAdjustmentDates(Long trustId);

    /**
     * 获取测算情景列表
     * @param trustId 产品ID
     * @param simulationDate 测算日期 yyyy-MM-dd
     * @return 情景列表
     */
    ResultInfo getScenarioList(Long trustId, String simulationDate);

    /**
     * 获取回购相关指标
     * @param taskSessionId 任务会话ID
     * @return 回购相关指标
     */
    ResultInfo getRepurchaseMetrics(String taskSessionId, String simulationDate);
    /**
     * 获取回购折价率
     * @param taskSessionId 任务会话ID
     * @return 回购折价率明细
     */
    ResultInfo getRepurchaseAdjustmentRate(String taskSessionId, String simulationDate);
    /**
     * 更新回购折价率
     * @param params 包含trustId, M0~M6Plus等折价率参数
     * @return 更新结果
     */
    ResultInfo updateRepurchaseAdjustmentRate(Map<String, Object> params);

    /**
     * 获取情景回购信息展示弹窗数据
     * @param trustId 产品ID
     * @param taskSessionId 任务会话ID
     * @return 情景回购信息弹窗数据
     */
    ResultInfo getScenarioRepurchaseInfo(Long trustId, String taskSessionId);

    /**
     * 获取回购折价率参考（本息）
     * @param taskSessionId 任务会话ID
     * @return 回购折价率参考（本息）明细数据
     */
    ResultInfo getRepurchaseDiscountRateReferencePI(String taskSessionId);

    /**
     * 获取产品的所有场景测算回购折价率（本息）计算结果
     *
     * @param page 分页参数
     * @param trustId 产品ID
     * @return 回购折价率（本息）计算结果
     */
    ResultInfo getAllScenarioMobPriceSummaryPI(Page<Map<String, Object>> page, Long trustId);
}
