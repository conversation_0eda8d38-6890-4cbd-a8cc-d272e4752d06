/*
 * Copyright (c) 2024 Sankuai, Inc. All Rights Reserved.
 */
package com.sankuai.ccpoperation.services.controller.services;


import com.sankuai.ccpoperation.services.constant.ResultInfo;
import com.sankuai.ccpoperation.services.mapper.SolutionRevolvingPlanMapper;
import com.sankuai.ccpoperation.services.service.ISolutionRevolvingPlanService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/Services/solutionRevolvingPlan")
public class SolutionRevolvingPlanController {

    @Resource
    ISolutionRevolvingPlanService solutionRevolvingPlanService;
    @Resource
    SolutionRevolvingPlanMapper solutionRevolvingPlanMapper;

    /**
     * 获取循环购买设置
     *
     * @param trustId 产品代码
     * @return 循环购买设置
     */
    @ApiOperation("循环购买设置列表")
    @GetMapping("/getSolutionRevolvingPool")
    public ResultInfo getSolutionRevolvingPool(@RequestParam(value = "trustId") String trustId) {
        return solutionRevolvingPlanService.getSolutionRevolvingPool(trustId);
    }

    /**
     * 获取合格投资回报设置
     *
     * @param trustId 产品代码
     * @return 循环购买设置
     */
    @ApiOperation("合格投资回报设置-1")
    @GetMapping("/getSolutionSettingCmb")
    public ResultInfo getSolutionSettingCmb(@RequestParam(value = "trustId") String trustId) {
        return solutionRevolvingPlanService.getSolutionSettingCmb(trustId);
    }

    /**
     * 获取合格投资回报设置
     *
     * @param trustId 产品代码
     * @return 循环购买设置
     */
    @ApiOperation("合格投资回报设置-2")
    @GetMapping("/getSolutionSetting")
    public ResultInfo getSolutionSetting(@RequestParam(value = "trustId") String trustId) {
        return solutionRevolvingPlanService.getSolutionSetting(trustId);
    }

    /**
     * 获取清仓回购设置
     *
     * @param trustId 产品代码
     * @return 循环购买设置
     */
    @ApiOperation("清仓回购设置")
    @GetMapping("/getScenarios")
    public ResultInfo getScenarios(@RequestParam(value = "trustId") String trustId) {
        return solutionRevolvingPlanService.getScenarios(trustId);
    }

    /**
     * 获取ext设置
     *
     * @param trustId 产品代码
     * @return 循环购买设置
     */
    @ApiOperation("ext设置")
    @GetMapping("/getExtSetting")
    public ResultInfo getExtSetting(@RequestParam(value = "trustId") String trustId) {
        return solutionRevolvingPlanService.getExtSetting(trustId);
    }

    /**
     * 获取触发事件设置
     *
     * @param trustId 产品代码
     * @return 循环购买设置
     */
    @ApiOperation("触发事件设置")
    @GetMapping("/getPortfolioPaymentSequence")
    public ResultInfo getPortfolioPaymentSequence(@RequestParam(value = "trustId") String trustId) {
        return solutionRevolvingPlanService.getPortfolioPaymentSequence(trustId);
    }

    /**
     * 获取次级溢价发行设置
     *
     * @param trustId 产品代码
     * @return 循环购买设置
     */
    @ApiOperation("次级溢价发行设置")
    @GetMapping("/getBondPremiumSettings")
    public ResultInfo getBondPremiumSettings(@RequestParam(value = "trustId") String trustId) {
        return solutionRevolvingPlanService.getBondPremiumSettings(trustId);
    }

    /**
     * 获取债券附加BP设置
     *
     * @param trustId 产品代码
     * @return 循环购买设置
     */
    @ApiOperation("债券附加BP设置")
    @GetMapping("/getTrustBondExtraBpSettings")
    public ResultInfo getTrustBondExtraBpSettings(@RequestParam(value = "trustId") String trustId) {
        return solutionRevolvingPlanService.getTrustBondExtraBpSettings(trustId);
    }

    /**
     * 获取浮动利率设置
     *
     * @param trustId 产品代码
     * @return 循环购买设置
     */
    @ApiOperation("浮动利率设置")
    @GetMapping("/getFloatRateSettings")
    public ResultInfo getFloatRateSettings(@RequestParam(value = "trustId") String trustId) {
        return solutionRevolvingPlanService.getFloatRateSettings(trustId);
    }

    /**
     * 获取回购价格明细数据
     *
     * @param sessionId 会话ID
     * @return 回购价格明细数据
     */
    @ApiOperation("获取回购价格明细数据")
    @GetMapping("/getMobPriceSummary")
    public ResultInfo getMobPriceSummary(
            @ApiParam(value = "会话ID", required = true) @RequestParam(value = "sessionId") String sessionId) {
        return solutionRevolvingPlanService.getMobPriceSummary(sessionId);
    }

    /**
     * 获取产品的所有场景测算回购折价率计算结果
     *
     * @param trustId 产品ID
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 回购折价率计算结果
     */
    @ApiOperation("获取产品的所有场景测算回购折价率计算结果")
    @GetMapping("/getAllScenarioMobPriceSummary")
    public ResultInfo getAllScenarioMobPriceSummary(
            @ApiParam(value = "产品ID", required = true) @RequestParam(value = "trustId") Long trustId,
            @ApiParam(value = "页码", required = false, defaultValue = "1")
            @RequestParam(value = "pageNum", defaultValue = "1") Long pageNum,
            @ApiParam(value = "每页大小", required = false, defaultValue = "20")
            @RequestParam(value = "pageSize", defaultValue = "10") Long pageSize) {
        Page<Map<String, Object>> page = new Page<>();
        page.setCurrent(pageNum);
        page.setSize(pageSize);
        return solutionRevolvingPlanService.getAllScenarioMobPriceSummary(page, trustId);
    }

    /**
     * 获取业务要求和回购价格基准
     * @param trustId 产品ID
     * @return 业务要求和回购价格基准
     */
    @ApiOperation("获取业务要求和回购价格基准")
    @GetMapping("/getBusinessRule")
    public ResultInfo getBusinessRule(
            @ApiParam(value = "产品ID", required = true) @RequestParam(value = "trustId") Long trustId) {
        return solutionRevolvingPlanService.getBusinessRule(trustId);
    }

    /**
     * 获取回购折价率调优计算过的日期清单
     * @param trustId 产品ID
     * @return 日期清单
     */
    @ApiOperation("获取回购折价率调优计算过的日期清单")
    @GetMapping("/getRepurchaseAdjustmentDates")
    public ResultInfo getRepurchaseAdjustmentDates(
            @ApiParam(value = "产品ID", required = true) @RequestParam(value = "trustId") Long trustId) {
        return solutionRevolvingPlanService.getRepurchaseAdjustmentDates(trustId);
    }

    /**
     * 获取回购相关指标
     * @param taskSessionId 任务会话ID
     * @param simulationDate 测算日期
     * @return 回购相关指标
     */
    @ApiOperation("获取回购相关指标")
    @GetMapping("/getRepurchaseMetrics")
    public ResultInfo getRepurchaseMetrics(
            @ApiParam(value = "任务会话ID", required = true)
            @RequestParam(value = "taskSessionId") String taskSessionId,
            @ApiParam(value = "测算日期", required = true)
            @RequestParam(value = "simulationDate") String simulationDate) {
        return solutionRevolvingPlanService.getRepurchaseMetrics(taskSessionId, simulationDate);
    }

    /**
     * 获取回购折价率
     * @param taskSessionId 任务会话ID
     * @param simulationDate 测算日期
     * @return 回购折价率明细
     */
    @ApiOperation("获取回购折价率")
    @GetMapping("/getRepurchaseAdjustmentRate")
    public ResultInfo getRepurchaseAdjustmentRate(
            @ApiParam(value = "任务会话ID", required = true)
            @RequestParam(value = "taskSessionId") String taskSessionId,
            @ApiParam(value = "测算日期", required = true)
            @RequestParam(value = "simulationDate") String simulationDate) {
        return solutionRevolvingPlanService.getRepurchaseAdjustmentRate(taskSessionId, simulationDate);
    }

    /**
     * 更新回购折价率
     * @param params 包含trustId, M0~M6Plus等折价率参数
     * @return 更新结果
     */
    @ApiOperation("更新回购折价率")
    @PostMapping("/updateRepurchaseAdjustmentRate")
    public ResultInfo updateRepurchaseAdjustmentRate(@RequestBody Map<String, Object> params) {
        return solutionRevolvingPlanService.updateRepurchaseAdjustmentRate(params);
    }

    /**
     * 获取测算情景列表
     * @param trustId 产品ID
     * @param simulationDate 测算日期 yyyy-MM-dd
     * @return 情景列表
     */
    @ApiOperation("获取测算情景列表")
    @GetMapping("/getScenarioList")
    public ResultInfo getScenarioList(
            @ApiParam(value = "产品ID", required = true)
            @RequestParam(value = "trustId") Long trustId,
            @ApiParam(value = "测算日期", required = true)
            @RequestParam(value = "simulationDate") String simulationDate) {
        return solutionRevolvingPlanService.getScenarioList(trustId, simulationDate);
    }

    /**
     * 获取情景回购信息展示弹窗数据
     * @param trustId 产品ID
     * @param taskSessionId 任务会话ID
     * @return 情景回购信息弹窗数据
     */
    @ApiOperation("获取情景回购信息展示弹窗数据")
    @GetMapping("/getScenarioRepurchaseInfo")
    public ResultInfo getScenarioRepurchaseInfo(
            @ApiParam(value = "产品ID", required = true)
            @RequestParam(value = "trustId") Long trustId,
            @ApiParam(value = "任务会话ID", required = true)
            @RequestParam(value = "taskSessionId") String taskSessionId) {
        return solutionRevolvingPlanService.getScenarioRepurchaseInfo(trustId, taskSessionId);
    }

    /**
     * 获取回购折价率参考（本息）
     * @param taskSessionId 任务会话ID
     * @return 回购折价率参考（本息）明细数据
     */
    @ApiOperation("获取回购折价率参考（本息）")
    @GetMapping("/getRepurchaseDiscountRateReferencePI")
    public ResultInfo getRepurchaseDiscountRateReferencePI(
            @ApiParam(value = "任务会话ID", required = true)
            @RequestParam(value = "taskSessionId") String taskSessionId) {
        return solutionRevolvingPlanService.getRepurchaseDiscountRateReferencePI(taskSessionId);
    }

    /**
     * 获取产品的所有场景测算回购折价率（本息）计算结果
     *
     * @param trustId 产品ID
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 回购折价率（本息）计算结果
     */
    @ApiOperation("获取产品的所有场景测算回购折价率（本息）计算结果")
    @GetMapping("/getAllScenarioMobPriceSummaryPI")
    public ResultInfo getAllScenarioMobPriceSummaryPI(
            @ApiParam(value = "产品ID", required = true) @RequestParam(value = "trustId") Long trustId,
            @ApiParam(value = "页码", required = false, defaultValue = "1")
            @RequestParam(value = "pageNum", defaultValue = "1") Long pageNum,
            @ApiParam(value = "每页大小", required = false, defaultValue = "20")
            @RequestParam(value = "pageSize", defaultValue = "10") Long pageSize) {
        Page<Map<String, Object>> page = new Page<>();
        page.setCurrent(pageNum);
        page.setSize(pageSize);
        return solutionRevolvingPlanService.getAllScenarioMobPriceSummaryPI(page, trustId);
    }
}
