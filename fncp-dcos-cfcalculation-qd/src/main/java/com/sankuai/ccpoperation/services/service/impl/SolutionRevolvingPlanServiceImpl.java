/*
 * Copyright (c) 2024 Sankuai, Inc. All Rights Reserved.
 */
package com.sankuai.ccpoperation.services.service.impl;

import com.sankuai.ccpoperation.services.common.GroupIdUtils;
import com.sankuai.ccpoperation.services.constant.ResultInfo;
import com.sankuai.ccpoperation.services.constant.Status;
import com.sankuai.ccpoperation.services.entity.SolutionRevolvingPlan;
import com.sankuai.ccpoperation.services.entity.drb.MobPriceSummaryDTO;
import com.sankuai.ccpoperation.services.entity.drb.MobPriceExpandedDTO;
import com.sankuai.ccpoperation.services.mapper.SolutionRevolvingPlanMapper;
import com.sankuai.ccpoperation.services.service.ISolutionRevolvingPlanService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class SolutionRevolvingPlanServiceImpl
        extends ServiceImpl<SolutionRevolvingPlanMapper, SolutionRevolvingPlan> implements ISolutionRevolvingPlanService {

    @Resource
    SolutionRevolvingPlanMapper solutionRevolvingPlanMapper;

    /**
     * Description:循环购买设置列表
     */
    @Override
    public ResultInfo getSolutionRevolvingPool(String trustId) {
        List<List<Map<String, Object>>> list = new ArrayList<>();
        List<Map<String, Object>> structurePeriodsData =  solutionRevolvingPlanMapper.getStructurePeriodsData(trustId);
        List<Map<String, Object>> paymentTypeOptions =  solutionRevolvingPlanMapper.getPaymentTypeOptions();
        List<Map<String, Object>> templateOptions =  solutionRevolvingPlanMapper.getTemplateOptions();
        list.add(structurePeriodsData);
        list.add(paymentTypeOptions);
        list.add(templateOptions);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message,list);
    }

    /**
     * Description:合格投资回报设置
     */
    @Override
    public ResultInfo getSolutionSettingCmb(String trustId) {
        List<Map<String, Object>> solutionSettingCmb =  solutionRevolvingPlanMapper.getSolutionSettingCmb(trustId);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message,solutionSettingCmb);
    }

    /**
     * Description:清仓回购设置
     */
    @Override
    public ResultInfo getScenarios(String trustId) {
        List<List<Map<String, Object>>> list = new ArrayList<>();
        List<Map<String, Object>> conditionOptions = solutionRevolvingPlanMapper.getConditionOptions();
        List<Map<String, Object>> scenarios = solutionRevolvingPlanMapper.getScenarios(trustId);
        List<Map<String, Object>> containersOne = solutionRevolvingPlanMapper.getContainersOne(trustId);
        List<Map<String, Object>> containersTwo = solutionRevolvingPlanMapper.getContainersTwo(trustId);
        list.add(conditionOptions);
        list.add(scenarios);
        list.add(containersOne);
        list.add(containersTwo);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message,list);
    }

    @Override
    public ResultInfo getExtSetting(String trustId) {
        List<Map<String, Object>> extSetting = solutionRevolvingPlanMapper.getExtSetting(trustId);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message,extSetting);
    }

    @Override
    public ResultInfo getPortfolioPaymentSequence(String trustId) {
        List<List<Map<String, Object>>> list = new ArrayList<>();
        List<Map<String, Object>> list1 = new ArrayList<>();
        LinkedHashMap<String,Object> map = new LinkedHashMap<>();
        Boolean extSetting = solutionRevolvingPlanMapper.IsTopUpAvailable(trustId);
        if (extSetting) {
            map.put("TrustPeriod","RevolvingPeriod");
            map.put("PeriodName","循环期");
            list1.add(map);
        }
        map = new LinkedHashMap<>();
        map.put("TrustPeriod","AmotisationPeriod");
        map.put("PeriodName","摊还期");
        list1.add(map);
        list.add(list1);
        list1 = new ArrayList<>();

        map = new LinkedHashMap<>();
        map.put("ConditionCode","CumulativeDefaultRate");
        map.put("ConditionDesc","资产池累计损失率");
        list1.add(map);
        map = new LinkedHashMap<>();
        map.put("ConditionCode","M3DefaultRate");
        map.put("ConditionDesc","90+逾期率（M3）");
        list1.add(map);
        map = new LinkedHashMap<>();
        map.put("ConditionCode","PoolPrincipalBalance");
        map.put("ConditionDesc","资产池剩余本金规模");
        list1.add(map);
        map = new LinkedHashMap<>();
        map.put("ConditionCode","EquityTrancheBalance");
        map.put("ConditionDesc","次级债券本金余额");
        list1.add(map);
        list.add(list1);

        list1 = solutionRevolvingPlanMapper.getPaymentSequenceItems(trustId);
        list.add(list1);
        list1 = solutionRevolvingPlanMapper.getEventItems(trustId);
        list.add(list1);

        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message,list);
    }

    @Override
    public ResultInfo getBondPremiumSettings(String trustId) {
        List<List<Map<String, Object>>> list = new ArrayList<>();
        List<Map<String, Object>> list1;
        String tranche = solutionRevolvingPlanMapper.getTranche(trustId);
        if (tranche != null && tranche.indexOf(".") > 0) {
            list1 = solutionRevolvingPlanMapper.getBondNames(trustId);
        } else {
            list1 = solutionRevolvingPlanMapper.getBondNamesOther(trustId);
        }
        list.add(list1);
        list1 = solutionRevolvingPlanMapper.getStructurePremiumSettings(trustId);
        list.add(list1);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, list);
    }

    @Override
    public ResultInfo getTrustBondExtraBpSettings(String trustId) {
        List<List<Map<String, Object>>> list = new ArrayList<>();
        List<Map<String, Object>> list1 = solutionRevolvingPlanMapper.getTrustBondExtraBpSettings(trustId);
        list.add(list1);
        list1 = solutionRevolvingPlanMapper.getStructurePremiumSettings(trustId);
        list.add(list1);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, list);
    }

    @Override
    public ResultInfo getFloatRateSettings(String trustId) {
        List<List<Map<String, Object>>> list = new ArrayList<>();
        List<Map<String, Object>> list1 = solutionRevolvingPlanMapper.getBondsFloatBPDatas(trustId);
        list.add(list1);
        list1 = solutionRevolvingPlanMapper.getStructurePremiumSettings(trustId);
        list.add(list1);
        list1 = solutionRevolvingPlanMapper.getTrustFloatDatas(trustId);
        list.add(list1);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, list);
    }

    @Override
    public ResultInfo getSolutionSetting(String trustId) {
        List<Map<String, Object>> solutionSetting =  solutionRevolvingPlanMapper.getSolutionSetting(trustId);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message,solutionSetting);
    }

    @Override
    public ResultInfo getMobPriceSummary(String sessionId) {
        List<MobPriceSummaryDTO> mobPriceSummary =
                solutionRevolvingPlanMapper.getMobPriceSummary(sessionId);

        if (mobPriceSummary == null || mobPriceSummary.isEmpty()) {
            return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, new ArrayList<>());
        }

        String payDate = mobPriceSummary.get(0).getRepurchaseCalculationDate();
        Long trustId = mobPriceSummary.get(0).getTrustId();
        Long scenarioId = mobPriceSummary.get(0).getScenarioId();
        String category = mobPriceSummary.get(0).getCategory();
        List<MobPriceExpandedDTO> mobPriceExpanded =
                solutionRevolvingPlanMapper.getMobPriceExpanded(trustId, category, scenarioId, payDate);

        // 对mobPriceExpanded中的groupId进行转换
        for (MobPriceExpandedDTO dto : mobPriceExpanded) {
            dto.setGroupId(GroupIdUtils.replaceStr(dto.getGroupId(), dto.getProductNo()));
        }

        List<List<?>> list = new ArrayList<>();
        list.add(mobPriceSummary);
        list.add(mobPriceExpanded);

        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, list);
    }

    @Override
    public ResultInfo getAllScenarioMobPriceSummary(Page<Map<String, Object>> page, Long trustId) {
        IPage<Map<String, Object>> allScenarioMobPriceSummary =
                solutionRevolvingPlanMapper.getAllScenarioMobPriceSummary(page, trustId);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, allScenarioMobPriceSummary);
    }

    /**
     * 获取业务要求和回购价格基准
     * @param trustId 产品ID
     * @return 业务要求和回购价格基准
     */
    @Override
    public ResultInfo getBusinessRule(Long trustId) {
        Map<String, Object> result = solutionRevolvingPlanMapper.getBusinessRule(trustId);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, result);
    }

    /**
     * 获取回购折价率调优计算过的日期清单
     */
    @Override
    public ResultInfo getRepurchaseAdjustmentDates(Long trustId) {
        List<String> dateList = solutionRevolvingPlanMapper.getRepurchaseAdjustmentDates(trustId);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, dateList);
    }

    /**
     * 获取测算情景列表
     */
    @Override
    public ResultInfo getScenarioList(Long trustId, String simulationDate) {
        try {
            java.time.LocalDate simDate = java.time.LocalDate.parse(simulationDate);
            java.time.LocalDate today = java.time.LocalDate.now();

            List<Map<String, Object>> scenarioList;
            if (simDate.isEqual(today)) {
                // 当simulationDate等于今天时，查询step_id=1的记录
                scenarioList = solutionRevolvingPlanMapper.getScenarioListForToday(trustId, simulationDate);
            } else {
                // 否则查询is_final=1的记录
                scenarioList = solutionRevolvingPlanMapper.getScenarioListForOtherDates(trustId, simulationDate);
            }
            return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, scenarioList);
        } catch (Exception e) {
            return new ResultInfo(Status.REQUEST_PARAMETER_ERROR.code,
                    "simulationDate格式错误，应为yyyy-MM-dd", null);
        }
    }

    /**
     * 获取回购相关指标
     * @param taskSessionId 任务会话ID
     * @return 回购相关指标
     */
    @Override
    public ResultInfo getRepurchaseMetrics(String taskSessionId, String simulationDate) {
        List<Map<String, Object>> metrics =
                solutionRevolvingPlanMapper.getRepurchaseMetrics(taskSessionId, simulationDate);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, metrics);
    }

    /**
     * 获取回购折价率明细
     * @param taskSessionId 任务会话ID
     * @return 回购折价率明细
     */
    @Override
    public ResultInfo getRepurchaseAdjustmentRate(String taskSessionId, String simulationDate) {
        List<Map<String, Object>> list =
                solutionRevolvingPlanMapper.getRepurchaseAdjustmentRate(taskSessionId, simulationDate);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, list);
    }

    /**
     * 更新回购折价率
     * @param params 包含trustId, M0~M6Plus等折价率参数
     * @return 更新结果
     */
    @Override
    public ResultInfo updateRepurchaseAdjustmentRate(Map<String, Object> params) {
        // 参数校验
        if (params == null || !params.containsKey("TrustId")) {
            return new ResultInfo(Status.REQUEST_PARAMETER_ERROR.code, "缺少TrustId参数", null);
        }
        Integer trustId = null;
        try {
            trustId = Integer.parseInt(params.get("TrustId").toString());
        } catch (Exception e) {
            return new ResultInfo(Status.REQUEST_PARAMETER_ERROR.code, "TrustId参数格式错误", null);
        }
        // 构建更新参数
        String[] fields = {"M0","M1","M2","M3","M4","M5","M6","M6plus"};
        java.util.Map<String, Object> updateMap = new java.util.HashMap<>();
        for (String f : fields) {
            if (params.containsKey(f)) {
                updateMap.put(f, params.get(f));
            }
        }
        updateMap.put("DiscountRate", "OverdueArea");
        updateMap.put("TrustID", trustId);
        int affected = solutionRevolvingPlanMapper.updateRepurchaseAdjustmentRate(updateMap);
        if (affected > 0) {
            return new ResultInfo(Status.SUCCESS.code, "更新成功", null);
        } else {
            return new ResultInfo(Status.SYSTEM_ERROR.code, "更新失败，未找到对应记录", null);
        }
    }

    /**
     * 获取情景回购信息展示弹窗数据
     */
    @Override
    public ResultInfo getScenarioRepurchaseInfo(Long trustId, String taskSessionId) {
        List<Map<String, Object>> data = solutionRevolvingPlanMapper.getScenarioRepurchaseInfo(trustId, taskSessionId);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, data);
    }

    /**
     * 获取回购折价率参考（本息）
     */
    @Override
    public ResultInfo getRepurchaseDiscountRateReferencePI(String taskSessionId) {
        List<Map<String, Object>> data = solutionRevolvingPlanMapper.getRepurchaseDiscountRateReferencePI(taskSessionId);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, data);
    }

    @Override
    public ResultInfo getAllScenarioMobPriceSummaryPI(Page<Map<String, Object>> page, Long trustId) {
        IPage<Map<String, Object>> allScenarioMobPriceSummaryPI =
                solutionRevolvingPlanMapper.getAllScenarioMobPriceSummaryPI(page, trustId);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, allScenarioMobPriceSummaryPI);
    }
}
