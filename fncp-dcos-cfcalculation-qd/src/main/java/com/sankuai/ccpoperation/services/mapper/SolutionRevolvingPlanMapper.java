/*
 * Copyright (c) 2024 Sankuai, Inc. All Rights Reserved.
 */
package com.sankuai.ccpoperation.services.mapper;

import com.sankuai.ccpoperation.services.entity.SolutionRevolvingPlan;
import com.sankuai.ccpoperation.services.entity.drb.MobPriceSummaryDTO;
import com.sankuai.ccpoperation.services.entity.drb.MobPriceExpandedDTO;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

public interface SolutionRevolvingPlanMapper extends BaseMapper<SolutionRevolvingPlan> {

    List<Map<String, Object>> getStructurePeriodsData(String trustId);

    List<Map<String, Object>> getPaymentTypeOptions();

    List<Map<String, Object>> getTemplateOptions();

    List<Map<String, Object>> getSolutionSettingCmb(String trustId);

    List<Map<String, Object>> getConditionOptions();

    List<Map<String, Object>> getScenarios(String trustId);

    List<Map<String, Object>> getContainersOne(String trustId);

    List<Map<String, Object>> getContainersTwo(String trustId);

    List<Map<String, Object>> getExtSetting(String trustId);

    Boolean IsTopUpAvailable(String trustId);

    List<Map<String, Object>> getPaymentSequenceItems(String trustId);

    List<Map<String, Object>> getEventItems(String trustId);

    List<Map<String, Object>> getStructurePremiumSettings(String trustId);

    String getTranche(String trustId);

    List<Map<String, Object>> getBondNames(String trustId);

    List<Map<String, Object>> getBondNamesOther(String trustId);

    List<Map<String, Object>> getTrustBondExtraBpSettings(String trustId);

    List<Map<String, Object>> getBondsFloatBPDatas(String trustId);

    List<Map<String, Object>> getTrustFloatDatas(String trustId);

    List<Map<String, Object>> getSolutionSetting(String trustId);

    /**
     * 获取回购价格明细数据
     *
     * @param sessionId 会话ID
     * @return 回购价格明细数据
     */
    List<MobPriceSummaryDTO> getMobPriceSummary(@Param("sessionId") String sessionId);

    /**
     * 获取MOB价格扩展数据
     *
     * @param trustId 产品ID
     * @param category 会话ID
     * @param scenarioId 情景ID
     * @param payDate 付息日
     * @return MOB价格扩展数据
     */
    List<MobPriceExpandedDTO> getMobPriceExpanded(@Param("trustId") Long trustId,
                                                  @Param("category") String category,
                                                  @Param("scenarioId") Long scenarioId,
                                                  @Param("payDate") String payDate);

    /**
     * 获取产品的所有场景测算回购折价率计算结果（分页）
     *
     * @param page 分页参数
     * @param trustId 产品ID
     * @return 回购折价率计算结果
     */
    IPage<Map<String, Object>> getAllScenarioMobPriceSummary(Page<Map<String, Object>> page,
                                                             @Param("trustId") Long trustId);

    /**
     * 获取业务要求和回购价格基准
     * @param trustId 产品ID
     * @return 业务要求和回购价格基准
     */
    Map<String, Object> getBusinessRule(@Param("trustId") Long trustId);

    /**
     * 获取回购折价率调优计算过的日期清单
     * @param trustId 产品ID
     * @return 日期清单
     */
    List<String> getRepurchaseAdjustmentDates(@Param("trustId") Long trustId);

    /**
     * 获取测算情景列表 - 当日情景
     * @param trustId 产品ID
     * @param simulationDate 测算日期
     * @return 情景列表
     */
    List<Map<String, Object>> getScenarioListForToday(@Param("trustId") Long trustId,
                                                      @Param("simulationDate") String simulationDate);

    /**
     * 获取测算情景列表 - 其他日期情景
     * @param trustId 产品ID
     * @param simulationDate 测算日期
     * @return 情景列表
     */
    List<Map<String, Object>> getScenarioListForOtherDates(@Param("trustId") Long trustId,
                                                           @Param("simulationDate") String simulationDate);

    /**
     * 获取回购相关指标
     * @param taskSessionId 任务会话ID
     * @return 回购相关指标
     */
    List<Map<String, Object>> getRepurchaseMetrics(@Param("task_session_id") String taskSessionId,
                                                   @Param("simulation_start_date") String simulationStartDate);
    /**
     * 获取回购折价率明细
     * @param taskSessionId 任务会话ID
     * @return 回购折价率明细
     */
    List<Map<String, Object>> getRepurchaseAdjustmentRate(@Param("task_session_id") String taskSessionId,
                                                          @Param("simulation_start_date") String simulationStartDate);

    /**
     * 更新回购折价率
     * @param params 包含trustId, M0~M6Plus等折价率参数
     * @return 影响行数
     */
    int updateRepurchaseAdjustmentRate(@Param("params") Map<String, Object> params);

    /**
     * 获取情景回购信息展示弹窗数据
     * @param trustId 产品ID
     * @param taskSessionId 任务会话ID
     * @return 情景回购信息弹窗数据
     */
    List<Map<String, Object>> getScenarioRepurchaseInfo(@Param("trustId") Long trustId, @Param("taskSessionId") String taskSessionId);

    /**
     * 获取回购折价率参考（本息）
     * @param taskSessionId 任务会话ID
     * @return 回购折价率参考（本息）明细数据
     */
    List<Map<String, Object>> getRepurchaseDiscountRateReferencePI(@Param("taskSessionId") String taskSessionId);

    /**
     * 获取产品的所有场景测算回购折价率（本息）计算结果
     *
     * @param page 分页参数
     * @param trustId 产品ID
     * @return 回购折价率（本息）计算结果
     */
    IPage<Map<String, Object>> getAllScenarioMobPriceSummaryPI(Page<Map<String, Object>> page, @Param("trustId") Long trustId);
}
