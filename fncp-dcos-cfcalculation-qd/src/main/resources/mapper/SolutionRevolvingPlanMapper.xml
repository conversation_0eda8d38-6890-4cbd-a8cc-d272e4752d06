<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.ccpoperation.services.mapper.SolutionRevolvingPlanMapper">

    <select id="getStructurePeriodsData" resultType="java.util.Map">
        select AccountNo,
        RevolvingPeriodFrom,
        RevolvingPeriodTo,
        LoanTerm,
        LoanRemainingTerm,
        InterestRate,
        PaymentTypeCode,
        ifnull(ia.ItemAliasValue, i.ItemValue) as PaymentType,
        Percentage,
        ShortName,
        ifnull(RevolvingCount, 0)              as RevolvingCount
        from Analysis_SolutionRevolvingPlan p
        left join Analysis_Item i on p.PaymentTypeCode = i.ItemCode and i.CategoryId = 14
        left join Analysis_ItemAlias ia on i.ItemId = ia.ItemId
        left join (select RevolvingPeriod, count(*) as RevolvingCount
        from Analysis_RevolvingAnalysisConfig
        where TrustID = #{trustId}
        group by RevolvingPeriod) c on p.RevolvingPeriodFrom = c.RevolvingPeriod
        where p.TrustID = #{trustId}
        order by RevolvingPeriodFrom, LoanRemainingTerm desc, Percentage desc;
    </select>
    <select id="getPaymentTypeOptions" resultType="java.util.Map">
        select i.ItemCode as PaymentTypeCode, ifnull(ia.ItemAliasValue, i.ItemValue) as PaymentType
        from Analysis_Item i
        left join Analysis_ItemAlias ia
        on i.ItemId = ia.ItemId
        where i.CategoryId = 14
        order by i.ItemId;
    </select>
    <select id="getTemplateOptions" resultType="java.util.Map">
        select TemplateID, TemplateName
        from Analysis_TemplateDefinition
        where TemplateTypeCode = 'TrustRevolvingPool'
    </select>
    <select id="getSolutionSettingCmb" resultType="java.util.Map">
        select TrustID, TrustType, ReturnRate, InterestStartDay, InterestStartDay
        from Analysis_SolutionROISetting_CMB
        where TrustID = #{trustId};
    </select>
    <select id="getConditionOptions" resultType="java.util.Map">
        select N'OccurrenceDate' as ItemCode, N'日期' as ItemText, N'date' as ItemDataType
        union
        select N'BondBalance' as ItemCode, N'总债券余额' as ItemText, N'float' as ItemDataType
        union
        select N'PoolBalance' as ItemCode, N'资产池余额' as ItemText, N'float' as ItemDataType
        union
        select N'PoolBalance_Performing' as ItemCode, N'资产池余额(不含违约)' as ItemText, N'float' as ItemDataType;
    </select>
    <select id="getScenarios" resultType="java.util.Map">
        select ID, ScenarioName, ScenarioDescription
        from Analysis_BondRepurchaseSetting
        where TrustID = #{trustId};
    </select>
    <select id="getContainersOne" resultType="java.util.Map">
        select bs.ID as ScenarioID
        , ct.ContainerID
        , ct.ContainerType
        , ct.ContainerDesc
        , ct.PContainerID
        from Analysis_BondRepurchaseSetting bs
        join Analysis_CriteriaContainer ct
        on ct.BusinessType = N'BondRepurchase' and ct.BusinessID = bs.ID
        where bs.TrustID = #{trustId}
        order by bs.ID, ContainerID;
    </select>
    <select id="getContainersTwo" resultType="java.util.Map">
        select bs.ID as ScenarioID
        , cd.ContainerID
        , cd.ConditionID
        , cd.ConditionItem
        , cd.Operator
        , cd.Value
        from Analysis_BondRepurchaseSetting bs
        join Analysis_CriteriaContainer ct
        on ct.BusinessType = N'BondRepurchase' and ct.BusinessID = bs.ID
        join Analysis_CriteriaCondition cd
        on cd.BusinessType = ct.BusinessType and cd.BusinessID = ct.BusinessID and
        cd.ContainerID = ct.ContainerID
        where bs.TrustID = #{trustId}
        order by bs.ID, cd.ContainerID, cd.ConditionID
    </select>
    <select id="getExtSetting" resultType="java.util.Map">
        select Id, TrustID, CalculateBasis, DiscountRate, SpecifiedCallDiscount,
        CallAvailableAmount, CallPriceActual, M0, M1, M2, M3, M4, M5, M6, M6plus,
        TriggerNextMonth, is_calculate_repurchase_rate as IsCalculateRepurchaseRate,
        is_calculate_repurchase_date as IsCalculateRepurchaseDate
        from Analysis_BondRepurchaseSettingExt
        where TrustID = #{trustId}
    </select>
    <select id="IsTopUpAvailable" resultType="java.lang.Boolean">
        select IsTopUpAvailable
        from TrustManagement_Trust
        where TrustID = #{trustId};
    </select>
    <select id="getPaymentSequenceItems" resultType="java.util.Map">
        select ScenarioId as SequenceID, Name as SequenceDesc
        from TrustManagement_tblTrustPaymentScenario
        where TrustId = #{trustId}
        order by ScenarioId;
    </select>
    <select id="getEventItems" resultType="java.util.Map">
        select *
        from dbo_EventPaymentSequence
        where TrustID = #{trustId}
        order by PriorityLevel;
    </select>
    <select id="getStructurePremiumSettings" resultType="java.util.Map">
        select TrustBondID, ItemValue as BondName
        from TrustManagement_TrustBond
        where TrustId = #{trustId}
        and ItemCode = 'ShortName';
    </select>
    <select id="getTranche" resultType="java.lang.String">
        select Tranche
        from Analysis_SolutionBondPremiumSettings
        where TrustID = #{trustId};
    </select>
    <select id="getBondNames" resultType="java.util.Map">
        select a.*, ItemValue as BondName
        from Analysis_SolutionBondPremiumSettings a
        inner join TrustManagement_TrustBond b
        on a.Tranche = b.TrustBondId and a.TrustID = b.TrustId
        where a.TrustID = #{trustId}
        and b.ItemCode = 'ShortName';
    </select>
    <select id="getBondNamesOther" resultType="java.util.Map">
        select *
        from Analysis_SolutionBondPremiumSettings
        where TrustID = #{trustId};
    </select>
    <select id="getTrustBondExtraBpSettings" resultType="java.util.Map">
        select *
        from Analysis_SolutionBondExtraBPSettings
        where TrustID = #{trustId}
    </select>
    <select id="getBondsFloatBPDatas" resultType="java.util.Map">
        select *
        from Analysis_SolutionBondFloatRateSettings
        where TrustID = #{trustId};
    </select>
    <select id="getTrustFloatDatas" resultType="java.util.Map">
        select *
        from Analysis_FloatingRate
        where TrustID = #{trustId};
    </select>
    <select id="getSolutionSetting" resultType="java.util.Map">
        select * from Analysis_SolutionROISetting
        where TrustID=#{trustId};
    </select>

    <select id="getMobPriceSummary" resultType="com.sankuai.ccpoperation.services.entity.drb.MobPriceSummaryDTO">
        with cte as (
        select * from analysis_grouped_mob_price_summary
        where task_session_id = #{sessionId}
        ),
        details as (
        select repurchase_calculation_date as repurchaseCalculationDate, 'M0' as assetCategory, m0 as assetBalance,
        price_m0 as repurchasePrice, pricing_m0 as discountRate, trust_id, scenario_id, session_id as category
        from cte
        union all
        select repurchase_calculation_date as repurchaseCalculationDate, 'M1' as assetCategory, m1 as assetBalance,
        price_m1 as repurchasePrice, pricing_m1 as discountRate, trust_id, scenario_id, session_id as category
        from cte
        union all
        select repurchase_calculation_date as repurchaseCalculationDate, 'M2' as assetCategory, m2 as assetBalance,
        price_m2 as repurchasePrice, pricing_m2 as discountRate, trust_id, scenario_id, session_id as category
        from cte
        union all
        select repurchase_calculation_date as repurchaseCalculationDate, 'M3' as assetCategory, m3 as assetBalance,
        price_m3 as repurchasePrice, pricing_m3 as discountRate, trust_id, scenario_id, session_id as category
        from cte
        union all
        select repurchase_calculation_date as repurchaseCalculationDate, 'M4' as assetCategory, m4 as assetBalance,
        price_m4 as repurchasePrice, pricing_m4 as discountRate, trust_id, scenario_id, session_id as category
        from cte
        union all
        select repurchase_calculation_date as repurchaseCalculationDate, 'M5' as assetCategory, m5 as assetBalance,
        price_m5 as repurchasePrice, pricing_m5 as discountRate, trust_id, scenario_id, session_id as category
        from cte
        union all
        select repurchase_calculation_date as repurchaseCalculationDate, 'M6' as assetCategory, m6 as assetBalance,
        price_m6 as repurchasePrice, pricing_m6 as discountRate, trust_id, scenario_id, session_id as category
        from cte
        union all
        select repurchase_calculation_date as repurchaseCalculationDate, 'M6+' as assetCategory, m6plus as assetBalance,
        price_m6plus as repurchasePrice, pricing_m6plus as discountRate, trust_id, scenario_id, session_id as category
        from cte
        )
        select * from details
        union all
        select repurchaseCalculationDate, '总计' as assetCategory, sum(assetBalance) as assetBalance,
        sum(repurchasePrice) as repurchasePrice, null as discountRate,
        trust_id, scenario_id, category
        from details
        group by repurchaseCalculationDate, trust_id, scenario_id, category
    </select>

    <select id="getMobPriceExpanded" resultType="com.sankuai.ccpoperation.services.entity.drb.MobPriceExpandedDTO">
        select e.*, t.ItemValue as productNo
        from analysis_grouped_mob_price_expanded as e
        left join TrustManagement_TrustInfoExtension as t on e.trust_id = t.TrustId
        and t.ItemCode = 'ProductNo'
        where e.trust_id = #{trustId} and session_id = #{category} and scenario_id = #{scenarioId}
        and pay_date = #{payDate}
        order by e.group_id, e.seasoning;
    </select>

    <select id="getAllScenarioMobPriceSummary" resultType="java.util.Map">
        with cte as (
        select
        id,
        trust_id,
        case when session_id = 'StressTest' then '常规压测'
        when session_id = 'Multiple_DefaultPrepayment' then '压力倍数敏感性分析'
        when session_id = 'ProductDesign' then '多维度测算'
        else session_id end as session_id,
        scenario_id,
        scenario_desc,
        repurchase_calculation_date,
        simulation_start_date,
        m0,
        m1,
        m2,
        m3,
        m4,
        m5,
        m6,
        m6plus,
        pricing_m0,
        pricing_m1,
        pricing_m2,
        pricing_m3,
        pricing_m4,
        pricing_m5,
        pricing_m6,
        pricing_m6plus,
        price_m0,
        price_m1,
        price_m2,
        price_m3,
        price_m4,
        price_m5,
        price_m6,
        price_m6plus,
        baseline_scenario,
        case when session_id = 'StressTest' then 1
        when session_id = 'Multiple_DefaultPrepayment' then 3
        when session_id = 'ProductDesign' then 2 end as category_id,
        row_number() over(partition by trust_id, session_id order by id desc) as rn
        from analysis_grouped_mob_price_summary
        where trust_id = #{trustId}
        )
        select * from cte
        where category_id in (2, 3) or (category_id = 1 and rn = 1)
        order by category_id, scenario_id
    </select>
    <select id="getBusinessRule" parameterType="long" resultType="map">
        select case when s.CalculateBasis in ('PoolBalance', 'PoolBalance_Opening') then '本金余额'
        when s.CalculateBasis in ('PoolBalancePI', 'PoolBalancePI_Opening') then '本息余额'
        else s.CalculateBasis end as basis,
        b.IRR_Up as irr_up, b.IRR_Down as irr_down,
        b.VCProportion_Up as vc_proportion_up, b.VCProportion_Down as vc_proportion_down,
        b.ServiceRate_Up as service_rate_up, b.ServiceRate_Down as service_rate_down
        from Analysis_BondRepurchaseSettingExt as s
        left join Analysis_ProductDesignBusiness as b on s.TrustID = b.TrustID
        where s.TrustID = #{trustId}
    </select>
    <select id="getRepurchaseAdjustmentDates" resultType="java.lang.String">
        select distinct DATE_FORMAT(simulation_start_date, '%Y-%m-%d') as simulation_start_date
        from repurchase_metric_adjustment
        where trust_id = #{trustId} and is_final = 1 and category = 'ProductDesign'
        order by simulation_start_date desc
    </select>
    <select id="getScenarioListForToday" resultType="java.util.Map">
        with cte as (
        select trust_id, scenario_id, task_session_id, baseline_scenario, updated_at,
        case when category = 'StressTest' then '常规压测'
        when category = 'Multiple_DefaultPrepayment' then '压力倍数敏感性分析'
        when category = 'ProductDesign' then '多维度测算'
        else category end as category,
        case when baseline_scenario = 1 then concat(scenario_desc, ' 基准情景: 是')
        when baseline_scenario = 0 then concat(scenario_desc, ' 基准情景: 否')
        else scenario_desc end as scenario_desc,
        case when category = 'StressTest' then 1
        when category = 'Multiple_DefaultPrepayment' then 3
        when category = 'ProductDesign' then 2 end as category_id,
        row_number() over(partition by trust_id, category order by id desc) as rn
        from repurchase_metric_adjustment
        where trust_id = #{trustId} and simulation_start_date = #{simulationDate} and step_id = 1
        and category in ('ProductDesign', 'StressTest')
        )
        select * from cte
        where category_id in (2, 3) or (category_id = 1 and rn = 1)
        order by category_id, scenario_id
    </select>

    <select id="getScenarioListForOtherDates" resultType="java.util.Map">
        with cte as (
        select trust_id, scenario_id, task_session_id, baseline_scenario, updated_at,
        case when category = 'StressTest' then '常规压测'
        when category = 'Multiple_DefaultPrepayment' then '压力倍数敏感性分析'
        when category = 'ProductDesign' then '多维度测算'
        else category end as category,
        case when baseline_scenario = 1 then concat(scenario_desc, ' 基准情景: 是')
        when baseline_scenario = 0 then concat(scenario_desc, ' 基准情景: 否')
        else scenario_desc end as scenario_desc,
        case when category = 'StressTest' then 1
        when category = 'Multiple_DefaultPrepayment' then 3
        when category = 'ProductDesign' then 2 end as category_id,
        row_number() over(partition by trust_id, category order by id desc) as rn
        from repurchase_metric_adjustment
        where trust_id = #{trustId} and simulation_start_date = #{simulationDate} and is_final = 1
        and category in ('ProductDesign', 'StressTest')
        )
        select * from cte
        where category_id in (2, 3) or (category_id = 1 and rn = 1)
        order by category_id, scenario_id
    </select>
    <select id="getRepurchaseMetrics" resultType="java.util.Map">
        select repurchase_calculation_date, payment_gap, repurchase_price,
        secondary_irr, secondary_allocation, var_comp_ratio_incl_excess, asset_service_fee,
        full_excess_service_fee, full_excess_service_fee_ratio, step_id, is_final
        from repurchase_metric_adjustment
        where task_session_id = #{task_session_id} and simulation_start_date = #{simulation_start_date}
        and (step_id = 1 or is_final = 1)
    </select>
    <select id="getRepurchaseAdjustmentRate" resultType="java.util.Map">
        select asset_category, balance, price, rate, adjustment_price, adjustment_rate
        from repurchase_adjustment
        where task_session_id = #{task_session_id} and simulation_start_date = #{simulation_start_date}
    </select>
    <update id="updateRepurchaseAdjustmentRate" parameterType="map">
        UPDATE Analysis_BondRepurchaseSettingExt
        <set>
            <if test="params.M0 != null">M0 = #{params.M0},</if>
            <if test="params.M1 != null">M1 = #{params.M1},</if>
            <if test="params.M2 != null">M2 = #{params.M2},</if>
            <if test="params.M3 != null">M3 = #{params.M3},</if>
            <if test="params.M4 != null">M4 = #{params.M4},</if>
            <if test="params.M5 != null">M5 = #{params.M5},</if>
            <if test="params.M6 != null">M6 = #{params.M6},</if>
            <if test="params.M6plus != null">M6plus = #{params.M6plus},</if>
            DiscountRate = 'OverdueArea'
        </set>
        WHERE TrustID = #{params.TrustID}
    </update>
    <select id="getScenarioRepurchaseInfo" resultType="java.util.Map">
        with cte as (
        select DATE_FORMAT(m.repurchase_calculation_date, '%Y-%m-%d') as repurchase_date,
        case when a.repurchase_price_basis like 'PoolBalancePI%' then '本息余额' else '本金余额' end as repurchase_price_basis,
        a.asset_category,
        a.balance,
        a.origin_price as price,
        a.origin_rate as discount_rate,
        a.forecasted_interest,
        a.origin_price_pi as price_pi,
        a.origin_rate_pi as discount_rate_pi
        from repurchase_adjustment as a
        inner join repurchase_metric_adjustment as m on a.task_session_id = m.task_session_id
        where a.task_session_id = #{taskSessionId} and a.trust_id = #{trustId} and m.step_id = 1
        order by a.asset_category
        )
        select * from cte
        union all
        select max(repurchase_date) as repurchase_date,
        max(repurchase_price_basis) as repurchase_price_basis, '总计' as asset_category,
        sum(balance) as balance, sum(price) as price, null as discount_rate,
        sum(forecasted_interest) as forecasted_interest,
        sum(price_pi) as price_pi, null as discount_rate_pi
        from cte
    </select>

    <select id="getRepurchaseDiscountRateReferencePI" resultType="java.util.Map">
        with cte as (
        select * from analysis_grouped_mob_price_pi_summary
        where task_session_id = #{taskSessionId}
        ),
        details as (
        select repurchase_calculation_date as repurchaseCalculationDate, 'M0' as assetCategory, m0 as assetBalance,
        price_m0 as repurchasePrice, pricing_m0 as discountRate, trust_id, scenario_id, session_id as category
        from cte
        union all
        select repurchase_calculation_date as repurchaseCalculationDate, 'M1' as assetCategory, m1 as assetBalance,
        price_m1 as repurchasePrice, pricing_m1 as discountRate, trust_id, scenario_id, session_id as category
        from cte
        union all
        select repurchase_calculation_date as repurchaseCalculationDate, 'M2' as assetCategory, m2 as assetBalance,
        price_m2 as repurchasePrice, pricing_m2 as discountRate, trust_id, scenario_id, session_id as category
        from cte
        union all
        select repurchase_calculation_date as repurchaseCalculationDate, 'M3' as assetCategory, m3 as assetBalance,
        price_m3 as repurchasePrice, pricing_m3 as discountRate, trust_id, scenario_id, session_id as category
        from cte
        union all
        select repurchase_calculation_date as repurchaseCalculationDate, 'M4' as assetCategory, m4 as assetBalance,
        price_m4 as repurchasePrice, pricing_m4 as discountRate, trust_id, scenario_id, session_id as category
        from cte
        union all
        select repurchase_calculation_date as repurchaseCalculationDate, 'M5' as assetCategory, m5 as assetBalance,
        price_m5 as repurchasePrice, pricing_m5 as discountRate, trust_id, scenario_id, session_id as category
        from cte
        union all
        select repurchase_calculation_date as repurchaseCalculationDate, 'M6' as assetCategory, m6 as assetBalance,
        price_m6 as repurchasePrice, pricing_m6 as discountRate, trust_id, scenario_id, session_id as category
        from cte
        union all
        select repurchase_calculation_date as repurchaseCalculationDate, 'M6+' as assetCategory, m6plus as assetBalance,
        price_m6plus as repurchasePrice, pricing_m6plus as discountRate, trust_id, scenario_id, session_id as category
        from cte
        )
        select * from details
        union all
        select repurchaseCalculationDate, '总计' as assetCategory, sum(assetBalance) as assetBalance,
        sum(repurchasePrice) as repurchasePrice, null as discountRate,
        trust_id, scenario_id, category
        from details
        group by repurchaseCalculationDate, trust_id, scenario_id, category
    </select>
    <select id="getAllScenarioMobPriceSummaryPI" resultType="java.util.Map">
        with cte as (
        select
        id,
        trust_id,
        case when session_id = 'StressTest' then '常规压测'
        when session_id = 'Multiple_DefaultPrepayment' then '压力倍数敏感性分析'
        when session_id = 'ProductDesign' then '多维度测算'
        else session_id end as session_id,
        scenario_id,
        scenario_desc,
        repurchase_calculation_date,
        simulation_start_date,
        m0,
        m1,
        m2,
        m3,
        m4,
        m5,
        m6,
        m6plus,
        pricing_m0,
        pricing_m1,
        pricing_m2,
        pricing_m3,
        pricing_m4,
        pricing_m5,
        pricing_m6,
        pricing_m6plus,
        price_m0,
        price_m1,
        price_m2,
        price_m3,
        price_m4,
        price_m5,
        price_m6,
        price_m6plus,
        interest_m0,
        interest_m1,
        interest_m2,
        interest_m3,
        interest_m4,
        interest_m5,
        interest_m6,
        interest_m6plus,
        baseline_scenario,
        case when session_id = 'StressTest' then 1
        when session_id = 'Multiple_DefaultPrepayment' then 3
        when session_id = 'ProductDesign' then 2 end as category_id,
        row_number() over(partition by trust_id, session_id order by id desc) as rn
        from analysis_grouped_mob_price_pi_summary
        where trust_id = #{trustId}
        )
        select * from cte
        where category_id in (2, 3) or (category_id = 1 and rn = 1)
        order by category_id, scenario_id
    </select>
</mapper>
