# coding=utf-8

'''
收益分配 - 业务流程代码
using example: python xxx/xxx/engine.py 'TaskCode:XM-LL-20191018_CashFlow_STBatch,SessionID:FB0511B7-98A4-4C1C-9055-91F1601EA618'
注：参数字符串中，采用 逗号 隔开每个 属性:值
'''

import os
import re
import sys
import copy
import timeit
import functools
import traceback
import importlib
import pandas as pd
from datetime import date
from datetime import datetime
import defusedxml.ElementTree as ET

'''
argv[0]是程序运行的默认参数，使用代码的人，无需理会
'''
# 脚本方式运行时，程序中相关的全局变量值设置。
#script_abspath = sys.argv[0]
script_abspath = os.path.abspath(__file__)
script_abspath_array = script_abspath.split('/') if '/' in script_abspath else script_abspath.split('\\')
script_folder = os.path.normpath('/'.join(script_abspath_array[0:-1]))
dc_folder = os.path.normpath('/'.join(script_abspath_array[0:-2])) # DCSlave folder path
root_folder = os.path.normpath('/'.join(script_abspath_array[0:-4])) # Client's parent folder path
distributed_folder = os.path.normpath('/'.join(script_abspath_array[0:-3])) # distributed script folder path
calculation_folder = os.path.join(script_folder, 'calculation')
generated_folder = os.path.join(script_folder, 'generated')
log_folder = os.path.join(script_folder, 'logs')
procedure_folder_dev = os.path.join(root_folder, 'services-biz-python-script')
procedure_folder_pro = os.path.join(root_folder, 'TSSWCFServices', 'services-biz-python-script')
distributed_procedure_folder = os.path.join(distributed_folder, 'services-biz-python-script')
sys.path.append(script_folder)
sys.path.append(calculation_folder) #add 'calculation' folder path to running env
sys.path.append(generated_folder) #add 'methods' folder path to running env
sys.path.append(procedure_folder_dev) #add 'methods' folder path to running env
sys.path.append(procedure_folder_pro) #add 'methods' folder path to running env
sys.path.append(distributed_procedure_folder) #add 'methods' folder path to running env

sys.path.append(dc_folder) #add '.\DCSlave' folder path to running env

import utilities1 as util
from PythonFiles.CommonHelper import CaculateUtil
from PythonFiles import MysqlAdapter2Java as MysqlAdapter
from Task_IncomeDistribution_SaveResult import func as Task_IncomeDistribution_SaveResult
from Analysis_usp_SaveScenarioSession_PyEngine import func as Analysis_usp_SaveScenarioSession_PyEngine
from Analysis_usp_CalculateBondCashflowStatus_Delete import func as Analysis_usp_CalculateBondCashflowStatus_Delete
from Task_IncomeDistribution_GetVariableView import func as Task_IncomeDistribution_GetVariableView
from Task_IncomeDistribution_GetRangeView import func as Task_IncomeDistribution_GetRangeView
from Analysis_Get_InputCashflows import func as Analysis_Get_InputCashflows
from Analysis_usp_MergeStressTestResults import func as Analysis_usp_MergeStressTestResults


running_statistics = []

def func_runningstats(func):
    global running_statistics
    def wrapper(*args, **kw):
        ts = timeit.default_timer()
        res = func(*args, **kw)
        te = timeit.default_timer()
        running_statistics.append("【%s】[%fs]"%(func.__name__ , te - ts))
        return res
    return wrapper


@func_runningstats
def generate_method_script(task, process_taskcode_id):
    try:
        task_code = task['TaskCode']

        sql="""
            select c.XMLSqlQueryEC
            from Task_Criteria c join Task_ProcessTask t on t.CriteriaSetId = c.CriteriaSetId
            where ProcessTaskCodeId = @ProcessTaskCodeId
            and IsCurrent = 1;
        """
        sql = MysqlAdapter.prepareSql(sql, params={"ProcessTaskCodeId":process_taskcode_id})
        rows = MysqlAdapter.commonExecuteGetData(sql)

        if len(rows) < 1:
            raise Exception("*ERROR*未能读取到[{0}]所对应的方法库数据，程序退出。".format(task_code))

        xml_data = rows[0][0]
        root = ET.fromstring(xml_data)
        ec_queries = root.iter("Query")

        script_name = task_code
        script_path = os.path.join(generated_folder, '{0}.py'.format(script_name))
        script_tmpl_path = os.path.join(calculation_folder, "_code_generate_tmpl.py")

        # 先删除，再创建，不会有错......
        if os.path.exists(script_path):
            os.remove(script_path)

        regFee = re.compile(r"^EC_([A-Za-z]+_Fee)_(\d+)$") #匹配费用EC函数
        regBondPrincipal = re.compile(r"^EC_AllocationAmount_(FirstClass|SubClass|EquityClass)_(\d+)$") #匹配债券应付本金EC函数
        regBondInterest = re.compile(r"^EC_CurrentPeriodInterest_(FirstClass|SubClass|EquityClass)_(\d+)$") #匹配债券应付利息EC函数
        with open(script_path, mode="w", encoding="utf-8") as f:
            with open(script_tmpl_path, mode="r", encoding="utf-8") as ft:
                f.write(ft.read())
                f.write("\n\n")
            for query in ec_queries:
                func_name = query.attrib.get('name')
                method_name = "def {0}(self, period_id, variable_view, range_view, cashflow, cashflowname):".format(func_name)
                method_body = query.text
                #动态添加输出日志装饰器
                if regFee.search(func_name):
                    f.write(" "*4 + "@lh.logFee\n")
                elif regBondPrincipal.search(func_name):
                    f.write(" "*4 + "@lh.logBondPrincipal\n")
                elif regBondInterest.search(func_name):
                    f.write(" "*4 + "@lh.logBondInterest\n")
                # f.write("{0}{1}\n".format(" "*4, row["MethodName"]))
                # methodbody = "\n".join(" "*4 + l for l in row["MethodBody"].split("\n"))
                # f.write("{0}\n\n".format(methodbody))
                method = "{0}{1}\n{2}\n\n" \
                        .format(" "*4
                                , method_name
                                ,"\n".join(" "*4 + l for l in method_body.split("\n")))
                f.write(method)

    except Exception as ex:
        raise Exception("*ERROR*加载方法库定义并生成方法库脚本时发生错误，程序退出："+str(ex))


@func_runningstats
def global_variable_init(task, process_taskcode_id):
    variable_view = {}
    range_view = {}
    df_cfms = pd.DataFrame()
    df_cfm_actions = pd.DataFrame()
    cashflow = {}
    try:
        task_code = task['TaskCode']
        scenarioid = task["ScenarioID"]
        rundistributed = int(task.get('RunDistributed', 0))
        end_period = int(task.get('EndPeriod', 0))

        rows = Task_IncomeDistribution_GetVariableView(params={
            "TaskCode":task_code,
            "ScenarioId":scenarioid,
            "RunDistributed":rundistributed
        })
        variable_view = dict(rows)

        cols, rows = Task_IncomeDistribution_GetRangeView(params={
            "TaskCode":task_code,
            "ScenarioId":scenarioid,
            "RunDistributed":rundistributed
        })
        # Pandas DataFrame pivot(旋转数据行列)
        df = MysqlAdapter.dbrc_to_pddataframe(rows, cols)

        #print(df)
        df_pivot = df.pivot(index="ItemName", columns="PeriodsId", values="ItemValue")
        df_pivot = df_pivot.fillna(0) #处理缺失值
        rv_list = list(zip(list(df_pivot.index), list(df_pivot.values)))
        cols_count = len(cols)
        if cols_count < 2:
            raise Exception("*ERROR*RangeView中无期数数据程序已退出，请检查当前信托设置。")


        if end_period == 0:
            end_period = cols_count - 1

        for item in rv_list:
            range_view[item[0]] = item[1]

        sql="""
            SELECT XMLProcessTask
            FROM Task_ProcessTask
            where ProcessTaskCodeId = @ProcessTaskCodeId
                and IsCurrent = 1;
        """
        sql = MysqlAdapter.prepareSql(sql, params={"ProcessTaskCodeId":process_taskcode_id})
        rows = MysqlAdapter.commonExecuteGetData(sql)

        if len(rows) < 1:
            raise Exception("*ERROR*未能读取到[{0}]所对应的模型数据，程序退出。".format(task_code))

        xml_data = rows[0][0]
        df_columns = ['SequenceNo', 'ActionCode', 'ActionDisplayName', 'FunctionName', 'IsDirectInput', 'IsCashFlowDisplay', 'MethodName', 'ActionType', 'InputType']
        df_cfms = pd.DataFrame(columns=df_columns)

        root = ET.fromstring(xml_data)
        for action in root.iter("Action"):
            res = {}
            res["SequenceNo"] = int(action.attrib.get("SequenceNo"))
            res["ActionCode"] = action.attrib.get("ActionCode")
            res["ActionDisplayName"] = action.attrib.get("ActionDisplayName")
            res["FunctionName"] = action.attrib.get("FunctionName")
            for parameter in action.iter("Parameter"):
                name = parameter.attrib.get("Name")
                if name in ('IsDirectInput', 'IsCashFlowDisplay', 'MethodName', 'ActionType', 'InputType'):
                    res[name] = parameter.attrib.get("Value")

            df_cfms = df_cfms.append(res, ignore_index=True)


        df_cfms = df_cfms.loc[(df_cfms["ActionCode"] == 'BondPayment') | (df_cfms["ActionType"] == 'CashFlow')].sort_values(by=['SequenceNo'], axis = 0, ascending = True)


        df_cfm_actions = df_cfms.loc[(df_cfms["IsCashFlowDisplay"] == 'true') | (df_cfms["ActionCode"] == 'BondPayment')]

        for i,action in df_cfms.iterrows():
            action_code = action["ActionCode"]
            # set DirectInput and others (init) value
            if action_code in range_view:
                cashflow[action_code] = copy.deepcopy(range_view[action_code])
            else:
                cashflow[action_code] = [0] * (end_period + 1)

        return variable_view, range_view, cashflow, df_cfms, df_cfm_actions

    except Exception as ex:
        traceback.print_exc(file=sys.stdout)
        raise Exception("*ERROR*加载全局数据时发生错误，程序退出："+str(ex))


@func_runningstats
def calculate_per_period_allactions(task, variable_view, range_view, cashflow, df_cfms, logs):
    funcs_instance = None
    start_period = int(task.get("StartPeriod",0))
    end_period = int(task.get("EndPeriod", 0))
    funcs_instance = load_cf_methods(task, df_cfms, logs)

    for period_id in range(start_period, end_period + 1): # range(periods_count):
        # 触发清仓回购后，不再进行后续的收益分配
        executionmode = task["ExecutionMode"]
        tocall = executionmode[1:2]
        pre_period_id = max(period_id - 1, 0) #前一期
        if tocall == "1" and int(cashflow["CallOptionTriggered"][pre_period_id]) == 1:
            break

        print("==========第{0}期运算==========".format(period_id))
        logs.append("="*20 + " 第{0}期运算 ".format(period_id) + "="*20)
        #计算每期情况
        calc_cfms(variable_view, range_view, cashflow, df_cfms, funcs_instance, start_period, period_id)

        # 微众清仓回购设置为当月触发或下月触发时，配平智能报表出入项
        # 2025-02-20: 美团需要测试两种场景
        # 1) 循环结束即清仓回购，使用回购价格+回收款
        # 2) 循环期结束的后一期清仓回购，仅使用回购价格，此时需要将回收款置为0
        calc_accout(variable_view, cashflow, period_id, tocall)

    #写入日志
    write_task_log(task, logs)

def calc_cfms(variable_view, range_view, cashflow, df_cfms, funcs_instance, start_period, period_id):
    for i,action in df_cfms.iterrows():
        action_code = action.ActionCode
        action_name = action.ActionDisplayName
        action_method_name = action.MethodName

        # 不计算下期应付
        if len(action_method_name) < 1 or action_method_name in ["CreateCashFlow", "ExecuteAll"] or "_NextDue" in action_method_name:
            continue

        func = getattr(funcs_instance, action_method_name, None)
        if not func and period_id == start_period: # 输出一次即可
            print('未找到科目[{0}]的方法定义：[{1}]'.format(action_name, action_method_name))

        try:
            res = func(period_id, variable_view, range_view, cashflow, action_code)
            if res:
                cashflow[action_code][period_id] = res
        except Exception as ex:
            traceback.print_exc(file=sys.stdout) # 抛出发生异常的调用堆栈
            print('*ERROR*运算科目[{0}]的方法[{1}]时发生错误：{2}'.format(action_name, action_method_name, str(ex)))

def load_cf_methods(task, df_cfms, logs):
    try:
        lib_name = task["TaskCode"] # task["MethodScriptFileName"]
        # lib_name = 'cf_task_code' # 指定脚本名称-不使用生成的方法库脚本
        funcs_module = importlib.import_module(lib_name)
        funcs_instance = funcs_module.CFActionCalcMethods(copy.deepcopy(df_cfms), logs)
    except Exception as ex:
        traceback.print_exc(file=sys.stdout) # 抛出发生异常的调用堆栈
        raise Exception('*ERROR*加载方法库[{0}]时发生错误，程序退出：{1}'.format(lib_name, str(ex)))
    return funcs_instance

def write_task_log(task, logs):
    today = date.today().strftime('%Y%m%d')
    time = datetime.now().strftime('%X').replace(':','')
    logfolder_path = os.path.join(log_folder, task["TaskCode"], today)
    mkdir(logfolder_path)
    logfile_path = os.path.join(logfolder_path, '{0}.log'.format(time))
    if os.path.exists(logfile_path):
        os.remove(logfile_path)
    with open(logfile_path, mode="w+", encoding="utf-8") as f:
        for log in logs:
            f.write(log + "\n")

def calc_accout(variable_view, cashflow, period_id, tocall):
    current_CallOptionTriggered = int(cashflow["CallOptionTriggered"][period_id])

    if tocall == "1" and current_CallOptionTriggered > 0:
        if ("CallAvailableAmount" in variable_view.keys() and variable_view["CallAvailableAmount"] != "CallPriceWithPoolCollection") or ("CallPriceBase" in variable_view.keys() and variable_view["CallPriceBase"] == "PoolBalance_Opening"):
            # 计划当期资产回收款
            cashflow['TrustPlanAccount_Interest_Collected'][period_id] = 0.
            cashflow['TrustPlanAccount_Principal_Collected'][period_id] = 0.
            cashflow['TrustPlanAccount_Total_Collected'][period_id] = 0.
            # 当期合格投资收入
            cashflow["InvestmentIncome_ToInterest_Input"][period_id] = 0.
            cashflow["InvestmentIncome_ToPrincipal_Input"][period_id] = 0.
            cashflow["InvestmentIncome_Total_Input"][period_id] = 0.

            if "TopUp_Collected_Principal" in cashflow.keys():
                cashflow["TopUp_Collected_Principal"][period_id] = 0.
                cashflow["TopUp_Collected_Interest"][period_id] = 0.


@func_runningstats
def cashflow_result_save(task, cashflow, df_cfm_actions,variable_view,range_view):
    try:
        sessionid = task["SessionID"]
        task_code = task['TaskCode']
        trustid = task["TrustID"]
        scenarioid = task["ScenarioID"]
        analysismode = task.get('AnalysisMode', 'StressTest')
        executionsessionid = task["ExecutionSessionID"]
        executionmode = task["ExecutionMode"]
        rundistributed = int(task.get('RunDistributed', 0))
        start_period = int(task.get('StartPeriod', 0))
        end_period = int(task.get('EndPeriod', 0))
        dim2_scenarioid = int(task.get('Dimension2ScenarioID', 0))

        step = 'cashflow result'
        # 常规压测、压力值敏感性分析
        calc_session_results(cashflow, df_cfm_actions, sessionid, start_period, end_period)

        #保存SessionContextResult
        step = "call Task_IncomeDistribution_SaveResult"
        Task_IncomeDistribution_SaveResult(
            params={
                "SessionID":sessionid,
                "TaskCode":task_code,
                "TrustID":trustid,
                "ScenarioID":scenarioid
            }
        )

        # 把参数保存到sessioncontext
        insert_tmpl = "INSERT INTO Task_SessionContext(SessionId, VariableName, VariableValue, VariableDataType, IsConstant, IsKey, KeyIndex) VALUES {0};"
        row_tmpl = "('{0}', '{1}', '{2}', '{3}', {4}, {5}, {6})"
        data_sql = ""
        sessionid = task.get("SessionID")
        if sessionid:
            data_rows = [row_tmpl.format(sessionid, key, value, 'nvarchar', 0, 0, 0) for key, value in task.items() if key != "SessionID"]
            data_sql = ",".join(data_rows)

        insert_data_sql(insert_tmpl, data_sql)

        step = calc_stress_test_results(task, variable_view, range_view, sessionid, trustid, scenarioid, analysismode, executionsessionid, executionmode, rundistributed, dim2_scenarioid)

    except Exception as ex:
        traceback.print_exc(file=sys.stdout) # 抛出发生异常的调用堆栈
        print("*ERROR*结果数据保存时,[{0}]发生错误:{1}".format(step, str(ex)))

def calc_session_results(cashflow, df_cfm_actions, sessionid, start_period, end_period):
    sql="""
        delete from Task_SessionContextResult where SessionId = '@SessionId';
    """
    sql = MysqlAdapter.prepareSql(sql, params={"SessionId":sessionid})
    MysqlAdapter.commonRunsql(sql)

    data = []

    for action in df_cfm_actions['ActionCode']:
        list_cashflow = cashflow[action]
        end_period = len(list_cashflow)
        for i in range(start_period, end_period):
            data.append({'ItemCode': action, 'ItemValue': list_cashflow[i], 'PeriodsId': i})

    df_cashflow = pd.DataFrame(data, columns=['ItemCode', 'ItemValue', 'PeriodsId'])

    # convert numeric ItemValues to 4 decimal places (if it is numeric)
    df_cashflow['ItemValue'] = df_cashflow['ItemValue'].apply(lambda x: CaculateUtil.Round(x, 6) if isinstance(x, float) else x)

    # lookup ActionDisplayName in df_cfm_actions based on ItemCode, then assign ActionDisplayName to ItemName
    df_cashflow = pd.merge(df_cashflow, df_cfm_actions[['ActionCode', 'ActionDisplayName']], left_on='ItemCode', right_on='ActionCode', how='left')
    df_cashflow = df_cashflow.drop(columns=['ActionCode'])
    df_cashflow = df_cashflow.rename(columns={'ActionDisplayName': 'ItemName'})

    df_cashflow['SessionId'] = sessionid
    # organise columns
    df_cashflow = df_cashflow[['SessionId', 'ItemName', 'ItemValue', 'PeriodsId']]

    MysqlAdapter.dataframe_tosql(df_cashflow, 'Task_SessionContextResult')

def calc_stress_test_results(task, variable_view, range_view, sessionid, trustid, scenarioid, analysismode, executionsessionid, executionmode, rundistributed, dim2_scenarioid):

    #清仓回购模式删除回购期数后的现金流
    to_call = executionmode[1:2]
    if to_call == "1":
        step = "call Analysis_usp_CalculateBondCashflowStatus_Delete"
        Analysis_usp_CalculateBondCashflowStatus_Delete(params={"SessionId":sessionid})

    #计算XIR,WAL,Loss
    step = "XIRRAndWAL"
    XIRRAndWAL(trustid, sessionid, 0.01, task, variable_view, range_view)

    step = "call Analysis_usp_SaveScenarioSession_PyEngine"
    dim1_variable = task.get('Dim1Variable', '')
    dim2_variable = task.get('Dim2Variable', '')
    Analysis_usp_SaveScenarioSession_PyEngine(
            params={
                "TrustID":trustid,
                "ExecutionSessionID":executionsessionid,
                "ScenarioID":scenarioid,
                "Dimension2ScenarioID":dim2_scenarioid,
                "Dim1Variable":dim1_variable,
                "Dim2Variable":dim2_variable,
                "SessionID":sessionid,
                "AnalysisMode":analysismode,
                "ExecutionMode":executionmode,
                "RunDistributed":rundistributed
            }
        )

    if analysismode == 'StressTest':
        # 合并结果
        Analysis_usp_MergeStressTestResults(params={
                "TrustID":trustid,
                "SessionID":executionsessionid,
                "ExecutionMode":executionmode
            })

    return step

def insert_data_sql(insert_tmpl, data_sql):
    if len(data_sql) > 1:
        sql = insert_tmpl.format(data_sql)
        MysqlAdapter.commonRunsql(sql)


#计算XIR,WAL,Loss
def XIRRAndWAL(trustid, sessionid, nGuessRate,task,variable_view,range_view):
    #获取cashflowsByDays
    res = Analysis_Get_InputCashflows(params={
        "TrustID":trustid,
        "SessionId":sessionid
    })

    bonds = [item[1] for item in res]
    func = lambda x,y:x if y in x else x + [y]
    bonds = functools.reduce(func, [[], ] + bonds)

    cashflowbydays = []
    xirr = 0.0
    wal = 0.0
    loss = 0.0
    sql = ''
    list_del = []
    list_ins = []
    for bond in bonds:
        cashflowbydays = [list(item) for item in res if item[1] == bond]
        #深拷贝保证数组不被函数体内的代码所改变
        xirr = util.CaculateXIRR(copy.deepcopy(cashflowbydays), copy.deepcopy(nGuessRate))
        wal = util.CaculateWAL(copy.deepcopy(cashflowbydays))
        loss = util.CaculateLoss(copy.deepcopy(cashflowbydays))
        list_del.append("'{0}IRR', '总IRR', '{0}WAL', '{0}POWAL', '{0}NPV', '{0}Loss'".format(bond))
        list_ins.append("('{0}', '{1}IRR', {2})".format(sessionid, bond, "%.6f"%float(xirr)))
        list_ins.append("('{0}', '{1}WAL', {2})".format(sessionid, bond, "%.6f"%float(wal)))
        list_ins.append("('{0}', '{1}Loss', {2})".format(sessionid, bond, "%.6f"%float(loss)))

    # 计算总的XIRR, 需要将各个债券的现金流合并
    res_df = pd.DataFrame(res, columns=['PeriodsId', 'BondName', 'Dayscount', 'Amount', 'Total', 'Principal'])
    res_df['Dayscount'] = res_df['Dayscount'].astype(int)
    res_df['Amount'] = res_df['Amount'].astype(float)
    res_df['Total'] = res_df['Total'].astype(float)
    res_df['Principal'] = res_df['Principal'].astype(float)

    # 按PeriodsId分组，将各个BondName债券的现金流合并, BondName赋值“总”
    grouped = res_df.groupby('PeriodsId').agg({
        'BondName': 'first',  # 保留第一个BondName
        'Dayscount': 'first',  # 保留第一个Dayscount
        'Amount': 'sum',  # 汇总Amount
        'Total': 'sum',  # 汇总Total
        'Principal': 'sum'  # 汇总Principal
    }).reset_index()
    grouped['BondName'] = '总'  # 将所有BondName设置为“总”
    cashflowbydays = grouped[['PeriodsId', 'BondName', 'Dayscount', 'Amount', 'Total', 'Principal']].values.tolist()
    xirr = util.CaculateXIRR(copy.deepcopy(cashflowbydays), copy.deepcopy(nGuessRate))
    list_ins.append("('{0}', '总IRR', {1})".format(sessionid, "%.6f"%float(xirr)))

    sql = """
        delete from Task_SessionVariableResult
        where SessionId = '{0}'
        and ItemName in ( {1});

        insert into Task_SessionVariableResult( SessionId, ItemName, ItemValue ) values {2};
    """.format(sessionid, ','.join(list_del), ','.join(list_ins))
    MysqlAdapter.commonRunsql(sql)

def mkdir(path):
    # 去除首位空格
    path=path.strip()
    # 去除尾部 \ 符号
    path=path.rstrip("\\")
    # 判断路径是否存在
    isExists=os.path.exists(path)
    # 判断结果
    if not isExists:
        # 如果不存在则创建目录,创建目录操作函数
        '''
        os.mkdir(path)与os.makedirs(path)的区别是,当父目录不存在的时候os.mkdir(path)不会创建，os.makedirs(path)则会创建父目录
        '''
        os.makedirs(path)


def main(task):
    print("====================收益分配程序运行====================")

    t0 = timeit.default_timer()

    logs = []

    task_code = task['TaskCode']
    sql="""
        select CodeDictionaryId from Task_CodeDictionary as d
        inner join Task_CodeCategory as c on d.CodeCategoryId = c.CodeCategoryId and c.CategoryCode = 'ProcessTaskType'
        where d.CodeDictionaryCode = '@TaskCode' limit 1;
    """
    sql = MysqlAdapter.prepareSql(sql, params={"TaskCode":task_code})
    process_taskcode_id = MysqlAdapter.commonExecuteGetData(sql)[0][0]

    t1 = timeit.default_timer()
    running_statistics.append("【解析json参数】[{0}s]".format(t1 - t0))

    generate_method_script(task, process_taskcode_id)

    variable_view, range_view, cashflow, df_cfms, df_cfm_actions = global_variable_init(task, process_taskcode_id)
    calculate_per_period_allactions(task, variable_view, range_view, cashflow, df_cfms, logs)
    cashflow_result_save(task, cashflow, df_cfm_actions,variable_view,range_view)

    te = timeit.default_timer()
    print("各步骤运行：%s"%(','.join(running_statistics)))
    print('====================收益分配程序运行结束，总计时：[%fs]===================='%(te - t0))

if __name__ == "__main__":

    if len(sys.argv) < 2 or len(sys.argv[1]) < 1:
        raise Exception("*ERROR*脚本参数未指定，程序退出")

    task_json = sys.argv[1]
    task = {}
    try:
        task_props = task_json.split(',')
        for p in task_props:
            pv = p.split(':')
            task[pv[0]] = pv[1]
    except Exception as err:
        raise Exception("*ERROR*解析传入参数[{0}]时发生错误，程序退出：{1}".format(task_json, str(err)))

    try:
        main(task)
    except Exception as exerr:
        print(str(exerr))
