--为缺少表注释的表添加注释
ALTER TABLE `Analysis_GroupedPoolCashflowAmortisationPlan` COMMENT='分析-分组资产池现金流摊销计划表';
ALTER TABLE `Analysis_TrustAssetStats` COMMENT='信托产品资产统计表';
ALTER TABLE `Cube_ResourcePool` COMMENT='分布式计算资源池表';
ALTER TABLE `data_ReserveFees` COMMENT='信托预留费用设定表';
ALTER TABLE `data_ReserveFeesCalculation` COMMENT='信托预留费用计算明细表';
ALTER TABLE `dbo_DynamicPoolProcessedData` COMMENT='动态资产池处理数据表';
ALTER TABLE `dbo_DynamicPoolRawData` COMMENT='动态资产池原始数据表';
ALTER TABLE `dbo_PoolDistributions` COMMENT='资产池分配策略表';
ALTER TABLE `dbo_StaticPoolLossDistribution` COMMENT='静态池损失分布统计表';
ALTER TABLE `dbo_StaticPoolLossSamples` COMMENT='静态池损失样本数据表';
ALTER TABLE `dbo_StaticPoolParams` COMMENT='静态池建模参数表';
ALTER TABLE `dbo_StaticPoolProcessedData` COMMENT='静态池处理数据表';
ALTER TABLE `dbo_StaticPoolRawData` COMMENT='静态池原始数据表';
ALTER TABLE `qd_calendar_datasource` COMMENT='产品日历事件数据源表';
ALTER TABLE `qd_wd_group_test_csl_001` COMMENT='工作日分组测试数据表001';
ALTER TABLE `qd_wd_group_test_csl_002` COMMENT='工作日分组测试数据表002';
ALTER TABLE `reserve_calculation` COMMENT='储备金计算结果表';
ALTER TABLE `sso_ticket` COMMENT='单点登录认证票据表';
ALTER TABLE `term_discount_factor` COMMENT='期限折现因子表';
ALTER TABLE `Analysis_PoolCashflowHistory` COMMENT='现金流已发生数据';
ALTER TABLE `Analysis_PoolCashflowHistory_all` COMMENT='现金流已发生数据';



